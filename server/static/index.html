<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOCX Viewer</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; }
        #content { border: 1px solid #ddd; padding: 20px; margin-top: 20px; }
        button { margin: 5px; }
    </style>
</head>
<body>
    <h1>DOCX Viewer</h1>
    <input type="file" id="fileInput" accept=".docx">
    <div>
        <button id="prevBtn">Previous</button>
        <span id="pageInfo"></span>
        <button id="nextBtn">Next</button>
    </div>
    <div id="content"></div>

    <script>
        let currentDocId = null;
        let currentPage = 1;
        let totalPages = 0;

        document.getElementById('fileInput').addEventListener('change', async (event) => {
            const file = event.target.files[0];
            if (file) {
                const formData = new FormData();
                formData.append('file', file);

                try {
                    const response = await fetch('/api/v0/parse/doc2html2', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'demo'
                        }
                    });
                    const result = await response.json();
                    if (result) {
                      const { docId } = result.data;
                        currentDocId = docId;
                        currentPage = 1;
                        await loadPage();
                    }
                } catch (error) {
                    console.error('Error uploading file:', error);
                }
            }
        });

        document.getElementById('prevBtn').addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                loadPage();
            }
        });

        document.getElementById('nextBtn').addEventListener('click', () => {
            if (currentPage < totalPages) {
                currentPage++;
                loadPage();
            }
        });

        async function loadPage() {
            try {
                const response = await fetch(`/api/v0/parse/doc2html2/${currentDocId}/${currentPage}`);
                const result = await response.json();
                totalPages = result.totalPages;
                console.log(result);
                document.getElementById('content').innerHTML = result.content;
                updatePageInfo();
            } catch (error) {
                console.error('Error loading page:', error);
            }
        }

        function updatePageInfo() {
            document.getElementById('pageInfo').textContent = `Page ${currentPage} of ${totalPages}`;
        }
    </script>
</body>
</html>

