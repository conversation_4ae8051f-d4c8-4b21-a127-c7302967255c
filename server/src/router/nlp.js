import { WF, RF } from '../lib/file'
// import req from 'koa2-request'
import uuid from 'uuid'
import htr from '../lib/htr'
import { isAllEmpty, getDbPath } from '../lib/tool'
import { useGuards, encrypt } from '../service'

const SPACE_NAME = 'nlp';

const adminRouter = (router, apiPath, io) => {
  const apiFn = (path) => `${apiPath}/${SPACE_NAME}/${path}`;

  // 添加模型
  router.post(apiFn('add'),
    useGuards(['0']),
    async ctx => {   
      const { name, cate, version, api } = ctx.request.body;
      if(isAllEmpty([name])) {
        return htr(ctx, 500, null, '模型名称不能为空')
      }
      const udb = ctx.request.header['x-requested-with'];
      const filePath = getDbPath(SPACE_NAME, udb, SPACE_NAME);
      const time = Date.now();
      const res = WF(filePath, {
        id: uuid(),
        name,
        cate,
        version,
        api,
        ct: time,
      }, 1);
      if(res) {
        return htr(ctx, 200, null, '创建成功');
      }else {
        return htr(ctx, 500, null, '数据错误');
      }
    }
  );

  // 修改模型
  router.put(apiFn('mod'),
    useGuards(['0']),
    async ctx => {   
      const { id, name, cate, version, api } = ctx.request.body;
      if(isAllEmpty([id, name])) {
        return htr(ctx, 500, null, '模型ID和模型名称不能为空')
      }
      const udb = ctx.request.header['x-requested-with'];
      const filePath = getDbPath(SPACE_NAME, udb, SPACE_NAME);
      const nlp = RF(filePath);
      const newNlp = nlp.map(v => {
        if(v.id === id) {
          return {
            ...v,
            name,
            cate,
            version,
            api,
          }
        }
        return v
      });
      const res = WF(filePath, newNlp)
      if(res) {
        return htr(ctx, 200, null, '修改成功');
      }else {
        return htr(ctx, 500, null, '服务器错误');
      }
    }
  );

  // 获取模型列表
  router.get(apiFn('list'),
    useGuards(['0']),
    async ctx => {   
      const udb = ctx.request.header['x-requested-with'];
      const filePath = getDbPath(SPACE_NAME, udb, SPACE_NAME);
      const nlp = RF(filePath);
      return htr(ctx, 200, nlp);
    }
  );

  // 删除模型
  router.delete(apiFn('del'),
    useGuards(['0']),
    async ctx => {
      const { id } = ctx.query;
      if(isAllEmpty([id])) {
        return htr(ctx, 500, null, '模型ID不能为空')
      }
      
      const udb = ctx.request.header['x-requested-with'];
      const filePath = getDbPath(SPACE_NAME, udb, SPACE_NAME);
      const data = RF(filePath);
      const nlp = data.filter(item => item.id !== id);
      const res = WF(filePath, nlp);
      if(res) {
        return htr(ctx, 200, null, '删除成功');
      }else {
        return htr(ctx, 500, null, '服务器错误');
      }
    }
  );
}

export default adminRouter