import glob from 'glob'
import { uploadSingleCatchError, delFile } from '../lib/file'
import config from '../config'
import htr from '../lib/htr'
import os from 'os'
import { resolve } from 'path'
import { v4 as uuidv4 } from 'uuid'
import fs from 'fs/promises'
import mammoth from 'mammoth'
// import exceljs from 'exceljs'
import { LRUCache as LRU } from 'lru-cache';
import path from 'path';


import { useGuards } from '../service'

const os_flag = (os.platform().toLowerCase() === 'win32') ? '\\' : '/'

const SPACE_NAME = 'parse'


async function extractStyles(page) {
    const operatorList = await page.getOperatorList();
    const styles = [];
    let currentStyle = {};

    for (let i = 0; i < operatorList.fnArray.length; i++) {
        const fn = operatorList.fnArray[i];
        const args = operatorList.argsArray[i];

        switch (fn) {
            case pdfjsLib.OPS.setFont:
                currentStyle.font = args[0];
                break;
            case pdfjsLib.OPS.setFillColor:
                currentStyle.color = `rgb(${Math.round(args[0] * 255)}, ${Math.round(args[1] * 255)}, ${Math.round(args[2] * 255)})`;
                break;
            case pdfjsLib.OPS.setFontSize:
                currentStyle.fontSize = args[0];
                break;
            // Add more style extractions as needed
        }

        if (Object.keys(currentStyle).length > 0) {
            styles.push({ ...currentStyle });
        }
    }

    return styles;
}

async function parsePageToHtml(pageData) {
    const { content, viewport, styles } = pageData;
    let html = `<div class="pdf-page" style="width:${viewport.width}px;height:${viewport.height}px;position:relative;">`;

    content.items.forEach((item, index) => {
        const style = styles[index] || {};
        const transform = item.transform;
        const x = transform[4];
        const y = viewport.height - transform[5];

        html += `<div style="position:absolute;left:${x}px;top:${y}px;font-family:${style.font || 'sans-serif'};font-size:${style.fontSize || 12}px;color:${style.color || 'black'};">${item.str}</div>`;
    });

    html += '</div>';
    return html;
}


// ------ 实现docx大文档解析 start --------

// 缓存文档章节
const cache = new LRU({
  max: 500,
  maxSize: 5000,
  sizeCalculation: (value, key) => {
    if(typeof value === 'string') {
        return Math.ceil(value.length / (1024 * 1024));
    }else {
        return 4000
    }

  },
});

// 自定义样式映射
const customStyleMap = `
b => strong
i => em
u => u
strike => s
p[style-name='Heading 1'] => h1
p[style-name='Heading 2'] => h2
p[style-name='Heading 3'] => h3
p[style-name='Heading 4'] => h4
p[style-name='Heading 5'] => h5
p[style-name='Heading 6'] => h6
`;

async function processDocx(filePath) {
  const docId = path.basename(filePath, '.docx');

  try {
    const options = {
      styleMap: customStyleMap,
      includeDefaultStyleMap: true,
      //处理图片
      convertImage: mammoth.images.imgElement(async (image) => {
        const imageBuffer = await image.read('base64');
        const base64img = "data:" + image.contentType + ";base64," + imageBuffer;
        // console.log(image)
        const newSrc = await saveBase64Image('demo', base64img);
          // console.log(newSrc, image);
          return { src: newSrc }   
      }),
      // 遍历文档内容
      transformDocument: (element) => {
        // 这个函数将被应用到文档中的每个元素
        return element;
      }
    }
    const result = await mammoth.convertToHtml({ path: filePath }, options);
    // 解析数学公式
    const html = result.value;
    const pages = splitIntoPages(html);
    cache.set(docId, pages);

    return docId;
  } catch (error) {
    console.error('Error processing document:', error);
    throw error;
  }
}


function splitIntoPages(html, charsPerPage = 4000) {
  const pages = [];
  let currentPage = '';
  const paragraphs = html.split('</p>');

  for (let p of paragraphs) {
    if (currentPage.length + p.length > charsPerPage && currentPage.length > 0) {
      pages.push(currentPage + '</p>');
      currentPage = '';
    }
    currentPage += p + '</p>';
  }

  if (currentPage.length > 0) {
    pages.push(currentPage);
  }

  return pages;
}

async function getPage(docId, pageNumber) {
  const pages = cache.get(docId);
  if (!pages) {
    throw new Error('Document not found in cache');
  }

  if (pageNumber < 1 || pageNumber > pages.length) {
    return null;
  }

  return pages[pageNumber - 1];
}

async function getTotalPages(docId) {
  const pages = cache.get(docId);
  if (!pages) {
    throw new Error('Document not found in cache');
  }

  return pages.length;
}


// ------ 实现docx大文档解析 end --------

async function saveBase64Image(space, base64String) {
  // 从base64字符串中提取MIME类型和实际的base64数据
  const matches = base64String.match(/^data:(.+);base64,(.+)$/)
  
  if (!matches) {
    throw new Error('Invalid base64 string')
  }

  const [, mimeType, base64Data] = matches

  // 根据MIME类型确定文件扩展名
  const extension = mimeType.split('/')[1]
  const fileName = `${uuidv4()}.${extension}`
  
  const imageDir = `../../public/uploads/${space}`
  const filePath = resolve(__dirname, imageDir, fileName)

  // 将base64数据写入文件
  await fs.writeFile(filePath, base64Data, 'base64')

  

  // 返回新的图片URL
  return `${config.staticPath}/uploads/${space}/${fileName}`
}

const uploadRouter = (router, apiPath) => {
    const apiFn = (path) => `${apiPath}/${SPACE_NAME}/${path}`;
    // 解析docx, 并将其转化为html
    router.post(
      apiFn('doc2html'), 
      uploadSingleCatchError,
      async ctx => {
          let { filename, path } = ctx.file;
          const udb = ctx.request.header['x-requested-with'];
          // let rootPath = resolve(__dirname, `../../public/uploads/${udb}`)
          
          // console.log(path);
          const result = await mammoth.convertToHtml({ path }, {
            //处理图片
            convertImage: mammoth.images.imgElement(async (image) => {
                const imageBuffer = await image.read('base64');
                const base64img = "data:" + image.contentType + ";base64," + imageBuffer;
                // console.log(image)
                const newSrc = await saveBase64Image(udb, base64img);
                  // console.log(newSrc, image);
                  return { src: newSrc }   
              })
          });
          
        return htr(ctx, 200, {filename, html: result.value})
      }
    );

    // 解析docx, 并将其转化为html
    router.post(
      apiFn('doc2html2'),
      uploadSingleCatchError,
      async ctx => {
          let { path } = ctx.file;
          const docId = await processDocx(path);
          const content = await getPage(docId, 1);
          const total = await getTotalPages(docId);

        return htr(ctx, 200, {docId, html: content, total})
      }
    );

    // 获取docx章节
    router.get(
      apiFn('doc2html2/:docId/:page'),
      async ctx => {
        const { docId, page } = ctx.params;
        const pageNumber = parseInt(page, 10);

        if (isNaN(pageNumber) || pageNumber < 1) {
          ctx.throw(400, 'Invalid page number');
        }

        try {
          const content = await getPage(docId, pageNumber);
          if (!content) {
            ctx.throw(404, 'Page not found');
          }
          return htr(ctx, 200, {html: content})
        } catch (error) {
          ctx.throw(500, 'Error retrieving page: ' + error.message);
        }
      }
    );

    // 读取所有文件
    router.get(apiFn('files'),
      useGuards(['0', '1']),
      ctx => {
          const files = glob.sync(`${config.publicPath}/uploads/*`)
          const result = files.map(item => {
              return `${config.staticPath}${item.split(`${os_flag}public`)[1]}`
          })
          return htr(ctx, 200, result)
      }
    );

    // 删除文件
    router.delete(apiFn('file/del'),
      useGuards(['0', '1']),
      async ctx => {
        const { id } = ctx.query
        if(id) {
            const err = await delFile(`${config.publicPath}/uploads/${id}`)
            if(!err) {
              return htr(ctx, 200, null, '删除成功')
            }else {
              return htr(ctx, 500, null, '文件不存在，删除失败')
            } 
        }else {
            return htr(ctx, 500, null, 'id不能为空')
        }  
      }
    );
}

export default uploadRouter