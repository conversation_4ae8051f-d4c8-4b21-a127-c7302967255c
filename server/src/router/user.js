import jsonwebtoken from 'jsonwebtoken'
import { WF, RF } from '../lib/file'
// import req from 'koa2-request'
import uuid from 'uuid'
import config from '../config'
import htr from '../lib/htr'
import { isAllEmpty, getDbPath } from '../lib/tool'
import { useGuards, useToken, encrypt } from '../service'

const SPACE_NAME = 'user';

const adminRouter = (router, apiPath, io) => {
  const apiFn = (path) => `${apiPath}/${SPACE_NAME}/${path}`;
  
  // 管理员登录
  router.post(apiFn('login'),
    async ctx => {
      const { name, pwd } = ctx.request.body;
      if(isAllEmpty([name, pwd])) {
        return htr(ctx, 500, null, '用户名/密码不能为空')
      }
      const udb = ctx.request.header['x-requested-with'];
      const filePath = getDbPath(SPACE_NAME, udb, 'user');
      const data = RF(filePath) || [];
      const _pwd = encrypt(pwd);
      const user = data.find(item => (item.name === name) && (item.pwd === _pwd));
      if(user) {
        const { id, name, role } = user;
        // 存储用户信息
        let info = {
          id,
          name,
          role,
          udb
        }

        let data = {
          name,
          role,
          id,
          // 签发 token，1天
          token: jsonwebtoken.sign(
            info,
            config.jwt_secret,
            { expiresIn: '30d' }
          )
        }
        return htr(ctx, 200, data, '登录成功');
      }else {
        return htr(ctx, 500, null, '用户名/密码错误')
      }
    }
  );

  // 管理员登出
  router.get(apiFn('logout'),
    async ctx => {
      const obj = await useToken(ctx);
      if(obj.name) {
        return htr(ctx, 200, null, '已退出');
      }else {
        return htr(ctx, 500, null, '用户信息不存在')
      }
    }
  );

  // 添加管理员
  router.post(apiFn('add'),
    useGuards(['0']),
    async ctx => {   
      const { name, pwd, role } = ctx.request.body;
      if(isAllEmpty([name])) {
        return htr(ctx, 500, null, '用户名不能为空')
      }
      const udb = ctx.request.header['x-requested-with'];
      const filePath = getDbPath(SPACE_NAME, udb, 'user');
      const _pwd = encrypt(pwd || '123456');
      const time = Date.now();
      const uid = uuid();
      const res = WF(filePath, {
        id: uid,
        name,
        pwd: _pwd,
        role,
        ct: time,
      }, 1);

      // 向用户列表添加管理员信息
      const userPath = getDbPath('track', udb, 'user');
      WF(userPath, {
        id: uid,
        name,
        role,
        ct: time,
      }, 1);
      if(res) {
        return htr(ctx, 200, null, '创建成功');
      }else {
        return htr(ctx, 500, null, '数据错误');
      }
    }
  );

  // 用户信息修改
  router.put(apiFn('mod'),
    useGuards(['0']),
    async ctx => {   
      const { id, name, pwd, role } = ctx.request.body;
      if(isAllEmpty([id, name])) {
        return htr(ctx, 500, null, '用户ID和用户名不能为空')
      }
      const udb = ctx.request.header['x-requested-with'];
      const filePath = getDbPath(SPACE_NAME, udb, 'user');
      const admin = RF(filePath);
      const newAdmin = admin.map(v => {
        if(v.id === id) {
          return {
            ...v,
            name,
            pwd: pwd ? encrypt(pwd) : v.pwd,
            role
          }
        }
        return v
      });
      const res = WF(filePath, newAdmin)
      if(res) {
        return htr(ctx, 200, null, '修改成功');
      }else {
        return htr(ctx, 500, null, '服务器错误');
      }
    }
  );

  // 获取管理员列表
  router.get(apiFn('list'),
    useGuards(['0']),
    async ctx => {   
      const udb = ctx.request.header['x-requested-with'];
      const filePath = getDbPath(SPACE_NAME, udb, 'user');
      const admin = RF(filePath);
      const result = admin.map(v => {
        return {
          ...v,
          pwd: '',
        }
      });
      return htr(ctx, 200, result);
    }
  );

  // 删除用户
  router.delete(apiFn('del'),
    useGuards(['0']),
    async ctx => {
      const { id } = ctx.query;
      if(isAllEmpty([id])) {
        return htr(ctx, 500, null, '用户ID不能为空')
      }
      
      const udb = ctx.request.header['x-requested-with'];
      const filePath = getDbPath(SPACE_NAME, udb, 'user');
      const data = RF(filePath);
      const user = data.filter(item => item.id !== id);
      const res = WF(filePath, user);
      if(res) {
        return htr(ctx, 200, null, '删除成功');
      }else {
        return htr(ctx, 500, null, '服务器错误');
      }
    }
  );
}

export default adminRouter