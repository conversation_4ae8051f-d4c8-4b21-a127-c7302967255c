import { WF, RF } from '../lib/file'
import htr from '../lib/htr'
import { auth, ssoAuth, useToken } from '../service'
import { isAllEmpty, getDbPath } from '../lib/tool'
import config from '../config'

const SPACE_NAME = 'ai';

const ai2textUrl = 'https://dashscope.aliyuncs.com/api/v1/apps/144963f7b8b44d4eabe09d2cf17ff867/completion';

const ai2Mind = 'https://dashscope.aliyuncs.com/api/v1/apps/b6e6c247015b4027801daf1c8df06a15/completion';

const aiReq = async (url, data) => {
    const resopnse = await fetch(url, {
        method: "POST",
        mode: "cors",
        cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
        // credentials: "same-origin", // include, *same-origin, omit
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${config.al_ty_ak}`,
        //   "X-DashScope-Async": "enable" // 文生图专用
        },
        redirect: "follow",
        // referrerPolicy: "no-referrer", 
        body: JSON.stringify(data), 
        }
    );
    return resopnse.json()
}


const aiRouter = (router, apiPath, io) => {
  const apiFn = (path) => `${apiPath}/${SPACE_NAME}/${path}`;
  /** 通义千问 */
  router.get(apiFn('tyqw'),
    ssoAuth,
    async ctx => {
      // 判断用户是否有用量
      const { id, cmb = 100 } = await useToken(ctx);
      if(isAllEmpty([id])) {
        return htr(ctx, 400, null, '缺少参数');
      }
      const udb = ctx.request.header['x-requested-with'];
      const userPath = getDbPath(SPACE_NAME, udb, 'user');
      const userData = RF(userPath, false, {});
      if(userData[id]) {
        if(userData[id].ai <= 0) {
          return htr(ctx, 500, null, '您的AI额度已用完, 请联系管理员');
        }
        userData[id].ai = userData[id].ai - 1;
        WF(userPath, userData);
      }else {
        userData[id] = {
          ai: cmb - 1
        }
        WF(userPath, userData);
      }
      const { text, type } = ctx.query;
      let url = '';
      if(type === 'text') {
        url = ai2textUrl;
      }else if(type === 'mind') {
        url = ai2Mind;
      }
        const resopnse = await aiReq(url, {
            "input":{
                "prompt": text
            }
        });
        const time = Date.now();
        const fid = `d${time}`;
        if(resopnse.output) {
          return htr(
            ctx, 
            200, 
            { 
              id: fid, 
              output: resopnse.output, 
              usage: resopnse.usage 
            } 
          );
        }else {
          return htr(
            ctx,
            500,
            null,
            '服务请求超时'
          );
        }
    }
  );

  /** 通义千问(免费) */
  router.get(apiFn('tyqw/free'),
    async ctx => {
      // 判断用户是否有用量
      const udb = ctx.request.header['x-requested-with'];
      const extraPath = getDbPath(SPACE_NAME, udb, 'free');
      const extraData = RF(extraPath, false, {
        count: 300
      });
      if(extraData.count <= 0) {
        return htr(ctx, 500, null, 'AI额度已用完, 请联系管理员');
      }else {
        extraData.count = extraData.count - 1;
        WF(extraPath, extraData);
      }
      const { text, type } = ctx.query;
      let url = '';
      if(type === 'text') {
        url = ai2textUrl;
      }else if(type === 'mind') {
        url = ai2Mind;
      }

        const resopnse = await aiReq(url, {
          "input":{
              "prompt": text
          }
        });
        
        const time = Date.now();
        const fid = `d${time}`;
        if(resopnse.output) {
          return htr(
            ctx, 
            200, 
            { 
              id: fid, 
              output: resopnse.output, 
              usage: resopnse.usage 
            } 
          );
        }
    }
  );
}


export default aiRouter