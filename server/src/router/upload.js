import glob from 'glob'
import { uploadSingleCatchError, delFile } from '../lib/file'
import config from '../config'
import htr from '../lib/htr'
import os from 'os'
import { useGuards } from '../service'

const os_flag = (os.platform().toLowerCase() === 'win32') ? '\\' : '/'

const uploadRouter = (router, apiPath) => {
  const apiFn = (path) => `${apiPath}/${path}`;
    // 上传文件
    router.post(apiFn('upload'), 
      useGuards(['0', '1']),
      uploadSingleCatchError,
      ctx => {
          let { filename, path, size } = ctx.file;
          let { source } = ctx.request.body || 'unknow';
          let url = `${config.staticDomain}${path.split(`${os_flag}public`)[1]}`;
          return htr(ctx, 200, {filename, url, source, size}, '文件上传成功')
      }
    );

    // 免费上传文件
    router.post(
      apiFn('upload/free'), 
      uploadSingleCatchError,
      ctx => {
          let { filename, path, size } = ctx.file;
          let { source } = ctx.request.body || 'unknow';

          let url = `${config.staticDomain}${path.split(`${os_flag}public`)[1]}`
          
          return htr(ctx, 200, {filename, url, source, size}, '文件上传成功')
      }
    );

    // 读取所有文件
    router.get(apiFn('files'),
      useGuards(['0', '1']),
      ctx => {
          const files = glob.sync(`${config.publicPath}/uploads/*`)
          const result = files.map(item => {
              return `${config.staticPath}${item.split(`${os_flag}public`)[1]}`
          })
          return htr(ctx, 200, result)
      }
    );

    // 删除文件
    router.delete(apiFn('file/del'),
      useGuards(['0', '1']),
      async ctx => {
        const { id } = ctx.query
        if(id) {
            const err = await delFile(`${config.publicPath}/uploads/${id}`)
            if(!err) {
              return htr(ctx, 200, null, '删除成功')
            }else {
              return htr(ctx, 500, null, '文件不存在，删除失败')
            } 
        }else {
            return htr(ctx, 500, null, 'id不能为空')
        }  
      }
    );
}

export default uploadRouter