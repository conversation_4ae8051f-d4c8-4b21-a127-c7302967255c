import * as cheerio from 'cheerio'

const crawlRouter = (router, apiPath, io) => {
    const apiFn = (path) => `${apiPath}/${path}`;
    /** 获取消息列表 */
    router.get(
      apiFn('fetchUrl'),
      async ctx => {
        const { url } = ctx.query;
        const htmlStr = await fetch(url).then(res => res.text());
        const $ = await cheerio.load(htmlStr);
        const title = $('title').first().text();
        const desc = $('meta[name=description]').attr('content');
        const icon = $('link[rel=icon]').attr('href');
        const host = new URL(url).origin;
        ctx.status = 200;
        ctx.body = {
            "success" : 1,
            "link": url,
            "meta": {
                title,
                "site_name" : title,
                "description" : desc,
                "image" : {
                    "url" : icon ? icon.includes('http') ? icon : `${host}/${icon}` : '/favicon.ico'
                }
            }
        }
      }
    );
  }
  
  
  export default crawlRouter