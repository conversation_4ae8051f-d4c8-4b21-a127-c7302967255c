import { WF, RF } from '../lib/file'
import htr from '../lib/htr'
import { isAllEmpty, getDbPath } from '../lib/tool'
import { useGuards } from '../service'

const SPACE_NAME = 'track';

const trackRouter = (router, apiPath, io) => {
  const apiFn = (path) => `${apiPath}/${SPACE_NAME}/${path}`;
  /** 获取通用埋点数据, 用户注册, 文件上传, AI用量 */
  router.get(apiFn('common'),
    useGuards(['0', '1']),
    async ctx => {
      const udb = ctx.request.header['x-requested-with'];
      const filePath = getDbPath(SPACE_NAME, udb, 'common');
      const data = RF(filePath, false, {
        users: 0,
        pays: 0,
        files: 0,
        aiCount: 0
      });
      return htr(ctx, 200, data);
    }
  );

  /** 通用埋点数据上报, 用户注册, 文件上传, AI用量 */
  router.post(apiFn('add'),
    async ctx => {
        const { type, data } = ctx.request.body;
        if(isAllEmpty([type])) {
            httpParmaError(ctx)
            return htr(ctx, 400)
        }
        const udb = ctx.request.header['x-requested-with'];
        const commonPath = getDbPath(SPACE_NAME, udb, 'common');
        const commonData = RF(commonPath, false, {});

        if(type === 'user') {
          const userPath = getDbPath(SPACE_NAME, udb, 'user');
          const ct = Date.now();
          // id, name, email, role
          const user = {
           ...data,
            ct
          }

          const myRes = WF(userPath, user, 1);
          if(myRes) {
            commonData.users = (commonData.users || 0) + 1;
            WF(commonPath, commonData);
            return htr(ctx, 200);
          }else {
            return htr(ctx, 500)
          }
        }else if(type === 'file') {
          commonData.files = (commonData.files || 0) + 1;
          WF(commonPath, commonData);
          return htr(ctx, 200);
        }else if(type === 'ai') {
          commonData.aiCount = (commonData.aiCount || 0) + 1;
          WF(commonPath, commonData);
          return htr(ctx, 200);
        }else if(type === 'pay') {
          const payPath = getDbPath(SPACE_NAME, udb, 'pay');
          const ct = Date.now();
          // id, name, email, role, pay
          const user = {
           ...data,
            ct
          }

          commonData.pays = (commonData.pays || 0) + 1;

          WF(payPath, user, 1);
          WF(commonPath, commonData);

          return htr(ctx, 200);
        }else {
          return htr(ctx, 500)
        }
    }
  );
}


export default trackRouter