import nodemailer from 'nodemailer'
import { RF, WF } from '../lib/file'
import htr from '../lib/htr'
import { getDbPath, isAllEmpty } from '../lib/tool'
import { useGuards } from '../service'
import uuid from 'uuid'
import config from '../config'

const SPACE_NAME = 'email';

const transporter = nodemailer.createTransport({
    host: "smtp.exmail.qq.com",
    port: 465,
    secure: true, // Use `true` for port 465, `false` for all other ports
    auth: {
      user: config.ms_email,
      pass: config.ms_token,
    },
  });


const emailRouter = (router, apiPath, io) => {
  const apiFn = (path) => `${apiPath}/${SPACE_NAME}/${path}`;

  // 发送邮件
  router.post(
    apiFn('send'),
    async ctx => {
        const { id, email, name, content } = ctx.request.body;
        if(isAllEmpty([id, email])) {
            return htr(ctx, 400);
        }
        // 定义transport对象并发送邮件
        try {
            let info = await transporter.sendMail({
                from: '"Flowmix Office Solution "<<EMAIL>>', // 发送方邮箱的账号
                to: email, // 邮箱接受者的账号
                subject: `Flowmix多模态基础服务: ${name}`, // Subject line
                text: `Flowmix多模态基础服务: ${name}`, // 文本内容
                html: content, // html 内容, 如果设置了html内容, 将忽略text内容
            });
    
            if(!info.response.includes('Ok')) {
                return htr(ctx, 500, null, '邮件发送失败');
            }

            // 修改消息状态
            const udb = ctx.request.header['x-requested-with'];
            const emialPath = getDbPath(SPACE_NAME, udb, 'list');
            const list = RF(emialPath) || [];
            const newList = list.map(item => {
                if(item.id === id) {
                    item.status = '1';
                }
                return item
            });

            WF(emialPath, newList);

            return htr(ctx, 200, null, '消息已发送至邮箱');
        } catch (error) {
            return htr(ctx, 500, null, '邮箱服务异常');
        }
    }
  );

  // POST 创建消息
  router.post(apiFn('add'),
    useGuards(['0', '1']),
    async ctx => {
        const { email, name, content } = ctx.request.body;
        if(isAllEmpty([email, name])) {
            return htr(ctx, 400)
        }
        const udb = ctx.request.header['x-requested-with'];
        const listPath = getDbPath(SPACE_NAME, udb, 'list');

        const ct = Date.now();

        // 将注册数据添加到用户列表
        const myRes = WF(listPath, {
          id: uuid(),
          name,
          email,
          content,
          status: "0", // 待发送0 已发送1
          ct
        }, 1);

        if(myRes) {
            return htr(ctx, 200);
        }else {
            return htr(ctx, 500)
        }
    }
  );

  // PUT 修改消息
  router.put(apiFn('mod'),
    useGuards(['0', '1']),
    async ctx => {
        const { id, name, email, content } = ctx.request.body;
        if(isAllEmpty([id, name, email])) {
            return htr(ctx, 400)
        }
        const udb = ctx.request.header['x-requested-with'];
        const listPath = getDbPath(SPACE_NAME, udb, 'list');

        const list = RF(listPath, false, []);

        // 将注册数据添加到用户列表
        const myRes = WF(listPath, list.map(item => {
          if(item.id === id) {
            item.name = name;
            item.email = email;
            item.content = content;
          }
          return item
        }));

        if(myRes) {
            return htr(ctx, 200);
        }else {
            return htr(ctx, 500)
        }
    }
  );

  /** 获取消息列表 */
  router.get(
    apiFn('list'),
    useGuards(['0', '1']),
    async ctx => {
      const udb = ctx.request.header['x-requested-with'];
      const listPath = getDbPath(SPACE_NAME, udb, 'list');
      const list = RF(listPath, false, []);
      return htr(ctx, 200, list);
    }
  );

  // POST 删除消息
  router.delete(apiFn('del'),
    useGuards(['0', '1']),
    async ctx => {
        const { id } = ctx.query;
        if(isAllEmpty([id])) {
            return htr(ctx, 400)
        }
        const udb = ctx.request.header['x-requested-with'];
        const listPath = getDbPath(SPACE_NAME, udb, 'list');

        const list = RF(listPath, false, []);

        // 将注册数据添加到用户列表
        const myRes = WF(listPath, list.filter(item => item.id!== id));

        if(myRes) {
            return htr(ctx, 200);
        }else {
            return htr(ctx, 500)
        }
    }
  );
}


export default emailRouter