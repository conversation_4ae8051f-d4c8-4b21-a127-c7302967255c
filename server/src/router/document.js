// 内存中存储文档列表（生产环境应该使用数据库）
let documents = [
  { id: 'test', name: '测试文档', createdAt: new Date().toISOString() },
  { id: 'experiment', name: '实验文档', createdAt: new Date().toISOString() },
  { id: 'tutorial', name: '教学H5文档教程', createdAt: new Date().toISOString() },
  { id: 'math', name: '数学课件设计', createdAt: new Date().toISOString() }
];

export default (router, prefix) => {
  // 获取文档列表
  router.get(`${prefix}/documents`, async (ctx) => {
    try {
      console.log('获取文档列表，当前文档数量:', documents.length);
      
      ctx.body = {
        code: 200,
        data: documents,
        message: '获取文档列表成功'
      };
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取文档列表失败',
        error: error.message
      };
    }
  });

  // 创建新文档
  router.post(`${prefix}/documents`, async (ctx) => {
    try {
      console.log('收到创建文档请求:', ctx.request.body);
      const { name } = ctx.request.body;
      
      if (!name) {
        console.log('文档名称为空');
        ctx.body = {
          code: 400,
          message: '文档名称不能为空'
        };
        return;
      }

      // 生成文档ID
      const docId = generateDocId();
      console.log('生成文档ID:', docId);
      
      const newDocument = {
        id: docId,
        name: name,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // 将新文档添加到文档列表中
      documents.push(newDocument);
      console.log('创建文档成功:', newDocument);
      console.log('当前文档列表长度:', documents.length);
      
      ctx.body = {
        code: 200,
        data: newDocument,
        message: '创建文档成功'
      };
    } catch (error) {
      console.error('创建文档失败:', error);
      ctx.body = {
        code: 500,
        message: '创建文档失败',
        error: error.message
      };
    }
  });

  // 删除文档
  router.delete(`${prefix}/documents/:id`, async (ctx) => {
    try {
      const { id } = ctx.params;
      
      // 从文档列表中删除文档
      const index = documents.findIndex(doc => doc.id === id);
      if (index !== -1) {
        documents.splice(index, 1);
        console.log(`删除文档成功: ${id}，剩余文档数量: ${documents.length}`);
        
        ctx.body = {
          code: 200,
          message: '删除文档成功'
        };
      } else {
        ctx.body = {
          code: 404,
          message: '文档不存在'
        };
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '删除文档失败',
        error: error.message
      };
    }
  });

  // 重命名文档
  router.put(`${prefix}/documents/:id`, async (ctx) => {
    try {
      const { id } = ctx.params;
      const { name } = ctx.request.body;
      
      if (!name) {
        ctx.body = {
          code: 400,
          message: '文档名称不能为空'
        };
        return;
      }

      // 在文档列表中找到并更新文档
      const docIndex = documents.findIndex(doc => doc.id === id);
      if (docIndex !== -1) {
        documents[docIndex].name = name;
        documents[docIndex].updatedAt = new Date().toISOString();
        
        console.log(`重命名文档成功: ${id} -> ${name}`);
        
        ctx.body = {
          code: 200,
          data: documents[docIndex],
          message: '重命名文档成功'
        };
      } else {
        ctx.body = {
          code: 404,
          message: '文档不存在'
        };
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '重命名文档失败',
        error: error.message
      };
    }
  });
};

// 生成文档ID的辅助函数
function generateDocId() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}