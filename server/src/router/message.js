import { WF, RF } from '../lib/file'
import htr from '../lib/htr'
import { isAllEmpty, getDbPath } from '../lib/tool'
import { useGuards } from '../service'
import sse from 'koa-sse-stream'
import uuid from 'uuid'

const SPACE_NAME = 'events';

const SSE_CONF = {
  maxClients: 2, // 最大连接数
  pingInterval: 10000 // 重连时间
}

const eventsRouter = (router, apiPath, io) => {
  const apiFn = (path) => `${apiPath}/${SPACE_NAME}/${path}`;
  /** 推送消息 */
  router.get(
    apiFn('on'),
    sse(SSE_CONF),
    async ctx => {
      // 发送消息给客户端
      const udb = ctx.request.header['x-requested-with'];
      const listPath = getDbPath(SPACE_NAME, udb, 'list');

      // 将注册数据添加到用户列表
      const list = RF(listPath, false, []);
      let curMes;

      const myRes = WF(listPath, list.map(item => {
        if(item.status === "0") {
          curMes = item;
          item.status = "1";
        }
        return item
      }));

      if(curMes) {
        ctx.sse.send(curMes);
      }else {
        ctx.sse.send(`[${Date.now()}] new client connected, msg is empty.`);
      }
    }
  );

  // POST 创建消息
  router.post(apiFn('add'),
    useGuards(['0', '1']),
    async ctx => {
        const { name, content } = ctx.request.body;
        if(isAllEmpty([name])) {
            return htr(ctx, 400)
        }
        const udb = ctx.request.header['x-requested-with'];
        const listPath = getDbPath(SPACE_NAME, udb, 'list');

        const ct = Date.now();

        // 将注册数据添加到用户列表
        const myRes = WF(listPath, {
          id: uuid(),
          name,
          content,
          status: "-1", // 待发送-1 已发送1, 发送中0
          ct
        }, 1);

        if(myRes) {
            return htr(ctx, 200);
        }else {
            return htr(ctx, 500)
        }
    }
  );

  // PUT 修改消息
  router.put(apiFn('mod'),
    useGuards(['0', '1']),
    async ctx => {
        const { id, name, content } = ctx.request.body;
        if(isAllEmpty([id, name])) {
            return htr(ctx, 400)
        }
        const udb = ctx.request.header['x-requested-with'];
        const listPath = getDbPath(SPACE_NAME, udb, 'list');

        const list = RF(listPath, false, []);

        // 将注册数据添加到用户列表
        const myRes = WF(listPath, list.map(item => {
          if(item.id === id) {
            item.name = name;
            item.content = content;
          }
          return item
        }));

        if(myRes) {
            return htr(ctx, 200);
        }else {
            return htr(ctx, 500)
        }
    }
  );

  // POST 发送消息
  router.put(apiFn('send'),
    useGuards(['0', '1']),
    async ctx => {
        const { id } = ctx.request.body;
        if(isAllEmpty([id])) {
            return htr(ctx, 400)
        }
        const udb = ctx.request.header['x-requested-with'];
        const listPath = getDbPath(SPACE_NAME, udb, 'list');

        // 将注册数据添加到用户列表
        const list = RF(listPath, false, []);

        const myRes = WF(listPath, list.map(item => {
          if(item.id === id) {
            item.status = "0";
          }
          return item
        }));

        if(myRes) {
            return htr(ctx, 200);
        }else {
            return htr(ctx, 500)
        }
    }
  );

  /** 获取消息列表 */
  router.get(
    apiFn('list'),
    useGuards(['0', '1']),
    async ctx => {
      const udb = ctx.request.header['x-requested-with'];
      const listPath = getDbPath(SPACE_NAME, udb, 'list');
      const list = RF(listPath, false, []);
      return htr(ctx, 200, list);
    }
  );

  // POST 删除消息
  router.delete(apiFn('del'),
    useGuards(['0', '1']),
    async ctx => {
        const { id } = ctx.query;
        if(isAllEmpty([id])) {
            return htr(ctx, 400)
        }
        const udb = ctx.request.header['x-requested-with'];
        const listPath = getDbPath(SPACE_NAME, udb, 'list');

        const list = RF(listPath, false, []);

        // 将注册数据添加到用户列表
        const myRes = WF(listPath, list.filter(item => item.id!== id));

        if(myRes) {
            return htr(ctx, 200);
        }else {
            return htr(ctx, 500)
        }
    }
  );
}


export default eventsRouter