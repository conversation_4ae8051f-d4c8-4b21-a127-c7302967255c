import { RF, WF } from '../lib/file'
import htr from '../lib/htr'
import {resolve} from "path"

const SPACE_NAME = 'mock';

const dbPath = resolve(process.cwd(), './public/db')

const mockRouter = (router, apiPath, io) => {
  const apiFn = (path) => `${apiPath}/${SPACE_NAME}/${path}`;
  /** 获取doc数据 */
  router.get(apiFn('doc'),
    async ctx => {
      const filePath = `${dbPath}/doc.json`;
      const data = RF(filePath, false, {});
      return htr(ctx, 200, data);
    }
  );
    /** 保存doc数据 */
    router.post(apiFn('doc'),
        async ctx => {
            const { config, schema, title } = ctx.request.body;
            const filePath = `${dbPath}/doc.json`;
            const data = WF(filePath, {
                config, schema, title
            });
            return htr(ctx, 200, data);
        }
    );
  // 获取board数据
  router.get(apiFn('board'),
    async ctx => {
        const filePath = `${dbPath}/board.json`;
        const data = RF(filePath, false);
        return htr(ctx, 200, data);
    }
  );
    // 获取mind数据
    router.get(apiFn('mind'),
        async ctx => {
            const filePath = `${dbPath}/mind.json`;
            const data = RF(filePath, false, {});
            return htr(ctx, 200, data);
        }
    );
}


export default mockRouter