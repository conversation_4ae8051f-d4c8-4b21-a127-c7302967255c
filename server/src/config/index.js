import { resolve } from 'path';
import { sourceTypes } from './var';
import { getIPAdress } from '../lib/tool';
const isDev = process.env.NODE_ENV === 'development';

const IP = getIPAdress();
const serverPort = isDev ? 3002 : 3002;
const staticPath = isDev ? `http://${IP}:${serverPort}` : `https://flowmix.turntip.cn:3002`;
const staticDomain = isDev ? `http://${IP}:${serverPort}` : `https://flowmix.turntip.cn`;
const publicPath = resolve(__dirname, '../../public');
const appStaticPath = resolve(__dirname, '../../static');
const routerPath = resolve(__dirname, '../router');

export default {
  isDev,
  protocol: 'http:',
  host: 'localhost',
  serverPort,
  staticPath,
  staticDomain,
  publicPath,
  appStaticPath,
  API_VERSION_PATH: '/api/v1',
  routerPath,
  sourceTypes,
  appId: '',
  appSecret: '',
  jwt_secret: 'cxzk_fe',
  // 阿里云
  al_ak: "",
  al_sk: "",
  // 阿里通义
  al_ty_ak: "",
  ms_email: "",
  ms_token: "",
}