// import { RF } from '../lib/upload'
import config from '../config'
import { getDbPath } from '../lib/tool'
import htr from '../lib/htr'
import { RF } from '../lib/file'
import { createHmac } from 'crypto'
import jsonwebtoken from 'jsonwebtoken'
import util from 'util'

const UID_KEY = 'X-Response-Uid';
const SSO_KEY = `ai-${UID_KEY}`;

const verify = util.promisify(jsonwebtoken.verify);

const useToken = async (ctx) => {
    const token = ctx.header.authorization;
    const payload = await verify(token.split(' ')[1], config.jwt_secret)
    return payload
}

const encrypt = (content) => {
    let hash = createHmac("md5", config.jwt_secret)
    hash.update(content)
    return hash.digest('hex')
}

const useGuards = (allRole) => {
    return async (ctx, next) => {
        const { role } = await useToken(ctx);
        if(allRole.includes(role)) {
            await next()
        }else {
            return htr(ctx, 406, null, '权限不足')
        }  
    }
}

// 通用鉴权服务
const auth = async (ctx, next) => {
    const { id, name } = await useToken(ctx);
    if(id && name) {
        ctx.set(UID_KEY, id);
        await next()
    }else {
        return htr(ctx, 403, null, '请先登录')
    }  
}

// 通用鉴权服务
const ssoAuth = async (ctx, next) => {
    const { id, name } = await useToken(ctx);
    const cache_id = ctx.cookies.get(SSO_KEY);
    if(cache_id && cache_id === id) {
        await next()
        return
    }

    const udb = ctx.request.header['x-requested-with'];
    const userPath = getDbPath('track', udb, 'user');
    const data = RF(userPath) || [];
    
    const user = data.find(item => item.id === id);
    if(user && user.id) {
        ctx.set(SSO_KEY, id, {
            maxAge: 1000 * 60 * 60 * 24 * 30,
            httpOnly: true,
            signed: true,
            overwrite: true
        });
        await next()
    }else {
        return htr(ctx, 403, null, '请先登录')
    }  
}

export {
    UID_KEY,
    encrypt,
    auth,
    useToken,
    useGuards,
    ssoAuth
}