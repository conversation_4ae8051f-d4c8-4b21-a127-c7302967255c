import { resolve } from 'path'

const dbPath = resolve(process.cwd(), './public/db')

const getDbPath = (namespace, appname, filename) => `${dbPath}/${namespace}/${appname}_${filename}.json`;

//获取本机ip地址
function getIPAdress() {
  var interfaces = require('os').networkInterfaces();　　
  for (var devName in interfaces) {　　　　
      var iface = interfaces[devName];　　　　　　
      for (var i = 0; i < iface.length; i++) {
          var alias = iface[i];
          if (alias.family === 'IPv4' && alias.address !== '127.0.0.1' && !alias.internal) {
              return alias.address;
          }
      }　　
  }
}

/**
 * 生成指定个数的随机字符转串
 * n 随机字符串的个数
 */
function generateRandomStr(n) {
  let str = 'abcdefghigklmnopqrstuvexyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890_-+%$';
  let res = ''
  let len = str.length;
  for(let i=0; i < n; i++) {
    res += str[Math.floor(Math.random() * len)]
  }
  return res
}

function isAllEmpty(arr) {
  return arr.some(v => v === '' || v === null || v === undefined)
}

function rand(min, max) {
  return Math.floor(Math.random()*(max-min))+min;
}

function base64urlEncodeObject(obj) {
  return btoa(JSON.stringify(obj)).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
}

function debounce(func, delay, immediate = false) {
    let timeout;

    return function(...args) {
        const context = this;

        // 如果需要立即执行
        if (immediate && !timeout) {
            func.apply(context, args);
        }

        // 清除之前的定时器
        clearTimeout(timeout);

        // 设置新的定时器
        timeout = setTimeout(() => {
            timeout = null;
            if (!immediate) {
                func.apply(context, args);
            }
        }, delay);
    };
}

export {
  generateRandomStr,
  isAllEmpty,
  rand,
  getIPAdress,
  base64urlEncodeObject,
  getDbPath,
  debounce
}