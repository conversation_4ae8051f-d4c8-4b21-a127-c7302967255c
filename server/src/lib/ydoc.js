import fs from "fs";
import * as Y from "yjs";
import path from "path";
import { debounce } from "./tool";
// 文件存储路径
const docDir = './doc-storage';

// 确保目录存在
fs.mkdirSync(docDir, { recursive: true });

// 存储文档与用户的映射关系
const docClients = new Map();
const clientInfo = new Map();
const docs = new Map(); // 存储活跃文档

// 文档存储路径
function getDocPath(docName) {
    return path.join(docDir, `${docName}.ydoc`);
}

// 加载文档
function loadDoc(docName) {
    const docPath = getDocPath(docName);

    // 如果文档已在内存中，直接返回
    if (docs.has(docName)) {
        return docs.get(docName);
    }

    const doc = new Y.Doc();

    // 如果文件存在，加载文档状态
    if (fs.existsSync(docPath)) {
        try {
            const data = fs.readFileSync(docPath);
            const update = new Uint8Array(data);
            Y.applyUpdate(doc, update);
            console.log(`已加载文档: ${docName}`);
        } catch (err) {
            console.error(`加载文档失败: ${docName}`, err);
        }
    } else {
        // 创建新文档并写入初始内容
        createNewDoc(docName, doc);
    }

    // 监听文档更新并保存
    doc.on('update', debounce(() => {
        const update = Y.encodeStateAsUpdate(doc);
        fs.writeFile(docPath, Buffer.from(update), (err) => {
            if (err) {
                console.error(`保存文档失败: ${docName}`, err);
            } else {
                console.log(`文档已保存: ${docName}`);
            }
        });
    }, 1000)); // 防抖保存，1秒内多次更新只保存一次

    docs.set(docName, doc);
    return doc;
}

// 创建新文档的具体实现
function createNewDoc(docName, doc) {
    const docPath = path.join(docDir, `${docName}.ydoc`);

    // 创建初始文档内容
    const yText = doc.getText('content');
    yText.insert(0, `# ${docName}\n\n欢迎使用新文档！创建时间: ${new Date().toLocaleString()}`);

    // 立即保存初始文档
    const update = Y.encodeStateAsUpdate(doc);
    fs.writeFile(docPath, Buffer.from(update), (err) => {});
}

export {
    docClients,
    clientInfo,
    loadDoc,
}