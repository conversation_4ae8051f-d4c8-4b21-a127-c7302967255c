{"config": {"bar": [{"type": "like", "config": {"flovers": 999}}, {"type": "review", "config": {"list": [], "count": 0}}, {"type": "barrage", "config": {"count": 0}}], "waterConfig": {"content": "flowmix", "fontSize": 16, "zIndex": 11, "rotate": -22, "gap": [100, 100], "offset": [0, 0]}}, "schema": [{"id": "EgGlpOE8kZ", "type": "alert", "data": {"type": "primary", "align": "left", "message": "flowmix/docx 是一款类似notion / 飞书文档的文档编辑引擎， 核心采用原生javascript实现，并基于模块化开发，更高效的植入任何web框架。个人或者企业可以基于此编辑器，轻松集成到自己的Web系统。"}, "tunes": {"indentTune": {"indentLevel": 0}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "IsQsJ00X1h", "type": "streamingBlock", "data": {"content": "flowmix/docx是一款<b>AI友好</b>的多模态<u>文档编辑器</u>，我们可以使用它轻松实现各种复杂的<b>文档系统</b>，<b>知识库管理平台</b>，同时支持源码交付和二次开发.  当前为一个AI流式输出的案例，它支持多组件异步流式输出，任务调度。快快离开", "type": "paragraph", "status": "Completed"}, "tunes": {"copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "TT3VddgGQD", "type": "paragraph", "data": {"text": "最近新增了【<b>文本绘图】</b>功能，我们可以采用<u class=\"cdx-underline___bk0Cn\">mermaid</u>语法轻松绘制图形：jk"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "PoMqx131-H", "type": "textDraw", "data": {"code": "pie title flowmix/docx多模态文档引擎\n    \"原生javascript\" : 400\n    \"框架\" : 85\n    \"模块化\" : 400\n", "theme": "default"}, "tunes": {"copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "qwBmHvK5Zg", "type": "paragraph", "data": {"text": "接下来我来带大家展示一下它的能力。"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "9YA_sdOKAm", "type": "paragraph", "data": {"text": "单选题(高等数学试卷公式渲染)"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "VRlrPjSL-O", "type": "paragraph", "data": {"text": "题目：一运动质点在某瞬时位于矢径 <span class=\"inline-math\" data-tex=\"\\overrightarrow{r}\\left( x,y \\right)\"><span class=\"katex\"><span class=\"katex-mathml\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><semantics><mrow><mover accent=\"true\"><mi>r</mi><mo stretchy=\"true\">→</mo></mover><mrow><mo fence=\"true\">(</mo><mi>x</mi><mo separator=\"true\">,</mo><mi>y</mi><mo fence=\"true\">)</mo></mrow></mrow><annotation encoding=\"application/x-tex\">\\overrightarrow{r}\\left( x,y \\right)</annotation></semantics></math></span><span class=\"katex-html\" aria-hidden=\"true\"><span class=\"base\"><span class=\"strut\" style=\"height: 1.2026em; vertical-align: -0.25em;\"></span><span class=\"mord accent\"><span class=\"vlist-t\"><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 0.9526em;\"><span class=\"\" style=\"top: -3em;\"><span class=\"pstrut\" style=\"height: 3em;\"></span><span class=\"mord mathnormal\" style=\"margin-right: 0.0278em;\">r</span></span><span class=\"svg-align\" style=\"top: -3.4306em;\"><span class=\"pstrut\" style=\"height: 3em;\"></span><span class=\"hide-tail\" style=\"height: 0.522em; min-width: 0.888em;\"><svg width=\"400em\" height=\"0.522em\" viewBox=\"0 0 400000 522\" preserveAspectRatio=\"xMaxYMin slice\"><path d=\"M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z\"></path></svg></span></span></span></span></span></span><span class=\"mspace\" style=\"margin-right: 0.1667em;\"></span><span class=\"minner\"><span class=\"mopen delimcenter\" style=\"top: 0em;\">(</span><span class=\"mord mathnormal\">x</span><span class=\"mpunct\">,</span><span class=\"mspace\" style=\"margin-right: 0.1667em;\"></span><span class=\"mord mathnormal\" style=\"margin-right: 0.0359em;\">y</span><span class=\"mclose delimcenter\" style=\"top: 0em;\">)</span></span></span></span></span></span>\n的端点处, 其速度大小为"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "eIZdLnDY63", "type": "paragraph", "data": {"text": "A. <span class=\"inline-math\" data-tex=\"\\frac{|d\\overrightarrow{r}|}{dt}\"><span class=\"katex\"><span class=\"katex-mathml\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><semantics><mrow><mfrac><mrow><mi mathvariant=\"normal\">∣</mi><mi>d</mi><mover accent=\"true\"><mi>r</mi><mo stretchy=\"true\">→</mo></mover><mi mathvariant=\"normal\">∣</mi></mrow><mrow><mi>d</mi><mi>t</mi></mrow></mfrac></mrow><annotation encoding=\"application/x-tex\">\\frac{|d\\overrightarrow{r}|}{dt}</annotation></semantics></math></span><span class=\"katex-html\" aria-hidden=\"true\"><span class=\"base\"><span class=\"strut\" style=\"height: 1.4968em; vertical-align: -0.345em;\"></span><span class=\"mord\"><span class=\"mopen nulldelimiter\"></span><span class=\"mfrac\"><span class=\"vlist-t vlist-t2\"><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 1.1518em;\"><span class=\"\" style=\"top: -2.655em;\"><span class=\"pstrut\" style=\"height: 3em;\"></span><span class=\"sizing reset-size6 size3 mtight\"><span class=\"mord mtight\"><span class=\"mord mathnormal mtight\">d</span><span class=\"mord mathnormal mtight\">t</span></span></span></span><span class=\"\" style=\"top: -3.23em;\"><span class=\"pstrut\" style=\"height: 3em;\"></span><span class=\"frac-line\" style=\"border-bottom-width: 0.04em;\"></span></span><span class=\"\" style=\"top: -3.485em;\"><span class=\"pstrut\" style=\"height: 3em;\"></span><span class=\"sizing reset-size6 size3 mtight\"><span class=\"mord mtight\"><span class=\"mord mtight\">∣</span><span class=\"mord mathnormal mtight\">d</span><span class=\"mord accent mtight\"><span class=\"vlist-t\"><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 0.9526em;\"><span class=\"\" style=\"top: -2.7em;\"><span class=\"pstrut\" style=\"height: 2.7em;\"></span><span class=\"mord mathnormal mtight\" style=\"margin-right: 0.0278em;\">r</span></span><span class=\"svg-align\" style=\"top: -3.1306em;\"><span class=\"pstrut\" style=\"height: 2.7em;\"></span><span class=\"hide-tail mtight\" style=\"height: 0.522em; min-width: 0.888em;\"><svg width=\"400em\" height=\"0.522em\" viewBox=\"0 0 400000 522\" preserveAspectRatio=\"xMaxYMin slice\"><path d=\"M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z\"></path></svg></span></span></span></span></span></span><span class=\"mord mtight\">∣</span></span></span></span></span><span class=\"vlist-s\">​</span></span><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 0.345em;\"><span class=\"\"></span></span></span></span></span><span class=\"mclose nulldelimiter\"></span></span></span></span></span></span>"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "P_YQ4U8j0w", "type": "paragraph", "data": {"text": "B. <span class=\"inline-math\" data-tex=\"\\frac{|d\\overrightarrow{r}|}{dt}\"><span class=\"katex\"><span class=\"katex-mathml\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><semantics><mrow><mfrac><mrow><mi mathvariant=\"normal\">∣</mi><mi>d</mi><mover accent=\"true\"><mi>r</mi><mo stretchy=\"true\">→</mo></mover><mi mathvariant=\"normal\">∣</mi></mrow><mrow><mi>d</mi><mi>t</mi></mrow></mfrac></mrow><annotation encoding=\"application/x-tex\">\\frac{|d\\overrightarrow{r}|}{dt}</annotation></semantics></math></span><span class=\"katex-html\" aria-hidden=\"true\"><span class=\"base\"><span class=\"strut\" style=\"height: 1.4968em; vertical-align: -0.345em;\"></span><span class=\"mord\"><span class=\"mopen nulldelimiter\"></span><span class=\"mfrac\"><span class=\"vlist-t vlist-t2\"><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 1.1518em;\"><span class=\"\" style=\"top: -2.655em;\"><span class=\"pstrut\" style=\"height: 3em;\"></span><span class=\"sizing reset-size6 size3 mtight\"><span class=\"mord mtight\"><span class=\"mord mathnormal mtight\">d</span><span class=\"mord mathnormal mtight\">t</span></span></span></span><span class=\"\" style=\"top: -3.23em;\"><span class=\"pstrut\" style=\"height: 3em;\"></span><span class=\"frac-line\" style=\"border-bottom-width: 0.04em;\"></span></span><span class=\"\" style=\"top: -3.485em;\"><span class=\"pstrut\" style=\"height: 3em;\"></span><span class=\"sizing reset-size6 size3 mtight\"><span class=\"mord mtight\"><span class=\"mord mtight\">∣</span><span class=\"mord mathnormal mtight\">d</span><span class=\"mord accent mtight\"><span class=\"vlist-t\"><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 0.9526em;\"><span class=\"\" style=\"top: -2.7em;\"><span class=\"pstrut\" style=\"height: 2.7em;\"></span><span class=\"mord mathnormal mtight\" style=\"margin-right: 0.0278em;\">r</span></span><span class=\"svg-align\" style=\"top: -3.1306em;\"><span class=\"pstrut\" style=\"height: 2.7em;\"></span><span class=\"hide-tail mtight\" style=\"height: 0.522em; min-width: 0.888em;\"><svg width=\"400em\" height=\"0.522em\" viewBox=\"0 0 400000 522\" preserveAspectRatio=\"xMaxYMin slice\"><path d=\"M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z\"></path></svg></span></span></span></span></span></span><span class=\"mord mtight\">∣</span></span></span></span></span><span class=\"vlist-s\">​</span></span><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 0.345em;\"><span class=\"\"></span></span></span></span></span><span class=\"mclose nulldelimiter\"></span></span></span></span></span></span>"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "KCoy5yPkm8", "type": "paragraph", "data": {"text": "C.\n<span class=\"inline-math\" data-tex=\"\\sqrt{\\left( \\frac{dx}{dt} \\right)^{2} + \\left( \\frac{dy}{dt} \\right)^{2}}\"><span class=\"katex\"><span class=\"katex-mathml\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><semantics><mrow><msqrt><mrow><msup><mrow><mo fence=\"true\">(</mo><mfrac><mrow><mi>d</mi><mi>x</mi></mrow><mrow><mi>d</mi><mi>t</mi></mrow></mfrac><mo fence=\"true\">)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo fence=\"true\">(</mo><mfrac><mrow><mi>d</mi><mi>y</mi></mrow><mrow><mi>d</mi><mi>t</mi></mrow></mfrac><mo fence=\"true\">)</mo></mrow><mn>2</mn></msup></mrow></msqrt></mrow><annotation encoding=\"application/x-tex\">\\sqrt{\\left( \\frac{dx}{dt} \\right)^{2} + \\left( \\frac{dy}{dt} \\right)^{2}}</annotation></semantics></math></span><span class=\"katex-html\" aria-hidden=\"true\"><span class=\"base\"><span class=\"strut\" style=\"height: 2.44em; vertical-align: -0.803em;\"></span><span class=\"mord sqrt\"><span class=\"vlist-t vlist-t2\"><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 1.637em;\"><span class=\"svg-align\" style=\"top: -4.4em;\"><span class=\"pstrut\" style=\"height: 4.4em;\"></span><span class=\"mord\" style=\"padding-left: 1em;\"><span class=\"minner\"><span class=\"minner\"><span class=\"mopen delimcenter\" style=\"top: 0em;\"><span class=\"delimsizing size1\">(</span></span><span class=\"mord\"><span class=\"mopen nulldelimiter\"></span><span class=\"mfrac\"><span class=\"vlist-t vlist-t2\"><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 0.8801em;\"><span class=\"\" style=\"top: -2.655em;\"><span class=\"pstrut\" style=\"height: 3em;\"></span><span class=\"sizing reset-size6 size3 mtight\"><span class=\"mord mtight\"><span class=\"mord mathnormal mtight\">d</span><span class=\"mord mathnormal mtight\">t</span></span></span></span><span class=\"\" style=\"top: -3.23em;\"><span class=\"pstrut\" style=\"height: 3em;\"></span><span class=\"frac-line\" style=\"border-bottom-width: 0.04em;\"></span></span><span class=\"\" style=\"top: -3.394em;\"><span class=\"pstrut\" style=\"height: 3em;\"></span><span class=\"sizing reset-size6 size3 mtight\"><span class=\"mord mtight\"><span class=\"mord mathnormal mtight\">d</span><span class=\"mord mathnormal mtight\">x</span></span></span></span></span><span class=\"vlist-s\">​</span></span><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 0.345em;\"><span class=\"\"></span></span></span></span></span><span class=\"mclose nulldelimiter\"></span></span><span class=\"mclose delimcenter\" style=\"top: 0em;\"><span class=\"delimsizing size1\">)</span></span></span><span class=\"msupsub\"><span class=\"vlist-t\"><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 1.0841em;\"><span class=\"\" style=\"top: -3.333em; margin-right: 0.05em;\"><span class=\"pstrut\" style=\"height: 2.7em;\"></span><span class=\"sizing reset-size6 size3 mtight\"><span class=\"mord mtight\"><span class=\"mord mtight\">2</span></span></span></span></span></span></span></span></span><span class=\"mspace\" style=\"margin-right: 0.2222em;\"></span><span class=\"mbin\">+</span><span class=\"mspace\" style=\"margin-right: 0.2222em;\"></span><span class=\"minner\"><span class=\"minner\"><span class=\"mopen delimcenter\" style=\"top: 0em;\"><span class=\"delimsizing size2\">(</span></span><span class=\"mord\"><span class=\"mopen nulldelimiter\"></span><span class=\"mfrac\"><span class=\"vlist-t vlist-t2\"><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 0.9322em;\"><span class=\"\" style=\"top: -2.655em;\"><span class=\"pstrut\" style=\"height: 3em;\"></span><span class=\"sizing reset-size6 size3 mtight\"><span class=\"mord mtight\"><span class=\"mord mathnormal mtight\">d</span><span class=\"mord mathnormal mtight\">t</span></span></span></span><span class=\"\" style=\"top: -3.23em;\"><span class=\"pstrut\" style=\"height: 3em;\"></span><span class=\"frac-line\" style=\"border-bottom-width: 0.04em;\"></span></span><span class=\"\" style=\"top: -3.4461em;\"><span class=\"pstrut\" style=\"height: 3em;\"></span><span class=\"sizing reset-size6 size3 mtight\"><span class=\"mord mtight\"><span class=\"mord mathnormal mtight\">d</span><span class=\"mord mathnormal mtight\" style=\"margin-right: 0.0359em;\">y</span></span></span></span></span><span class=\"vlist-s\">​</span></span><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 0.345em;\"><span class=\"\"></span></span></span></span></span><span class=\"mclose nulldelimiter\"></span></span><span class=\"mclose delimcenter\" style=\"top: 0em;\"><span class=\"delimsizing size2\">)</span></span></span><span class=\"msupsub\"><span class=\"vlist-t\"><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 1.354em;\"><span class=\"\" style=\"top: -3.6029em; margin-right: 0.05em;\"><span class=\"pstrut\" style=\"height: 2.7em;\"></span><span class=\"sizing reset-size6 size3 mtight\"><span class=\"mord mtight\"><span class=\"mord mtight\">2</span></span></span></span></span></span></span></span></span></span></span><span class=\"\" style=\"top: -3.597em;\"><span class=\"pstrut\" style=\"height: 4.4em;\"></span><span class=\"hide-tail\" style=\"min-width: 1.02em; height: 2.48em;\"><svg width=\"400em\" height=\"2.48em\" viewBox=\"0 0 400000 2592\" preserveAspectRatio=\"xMinYMin slice\"><path d=\"M424,2478\nc-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514\nc0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20\ns-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121\ns209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081\nl0 -0c4,-6.7,10,-10,18,-10 H400000\nv40H1014.6\ns-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185\nc-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2z M1001 80\nh400000v40h-400000z\"></path></svg></span></span></span><span class=\"vlist-s\">​</span></span><span class=\"vlist-r\"><span class=\"vlist\" style=\"height: 0.803em;\"><span class=\"\"></span></span></span></span></span></span></span></span></span>"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "tX7Ik_4b0o", "type": "paragraph", "data": {"text": "答案：A"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "qwBmHvK5Zbb", "type": "paragraph", "data": {"text": "接下来是一个可自由拖拽, 对齐的图片组件案例。"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "FfcPMn5ouQ", "type": "image", "data": {"caption": "", "withBorder": false, "withBackground": false, "stretched": false, "width": "", "height": "", "file": {"key": "FhEUfJDeuAHF7GHIDy7B59VA-iNw", "hash": "FhEUfJDeuAHF7GHIDy7B59VA-iNw", "fsize": 55471, "url": "http://cdn.dooring.cn/FhEUfJDeuAHF7GHIDy7B59VA-iNw"}, "alignLeft": false, "alignCenter": true, "alignRight": false}, "tunes": {"copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "cWD3obo-5x", "type": "paragraph", "data": {"text": "思维导图案例如下:"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "QMB_cMK9un", "type": "proMind", "data": {"id": "wep_851730125963119"}, "tunes": {"copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "qmWT1PCIXX", "type": "header", "data": {"text": "1.富文本内容能力的增强", "level": 2}, "tunes": {"indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}}}, {"id": "YW2E6Pdr2R", "type": "paragraph", "data": {"text": "这是一段文本，包含丰富的元素。<code class=\"inline-code\">Doc</code>是我们常用文档格式之一，<b>文档引擎</b>是一种文档集成的底层建设。它提供一中企业和个人能轻松接入集成的<mark class=\"cdx-marker___w6F33\" style=\"background-color: rgb(26, 73, 213);\"><span class=\"undefined\" style=\"color: rgb(243, 242, 242);\">源码级</span></mark>交付模式，目前文档支持<span class=\"cdx-paper___V3sSB\">多模态<span class=\"pop___AhfEX\">内容支持视频，音频，网页，图表等载体。</span></span>的内容体系。"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "evbteM_njW", "type": "paragraph", "data": {"text": "同时还支持<b><span class=\"undefined\" style=\"color: rgb(16, 12, 212);\">文本颜色</span></b>，<u class=\"cdx-underline___bk0Cn\">下划线</u>，<a href=\"http://doc.dooring.vip\" target=\"_blank\">文字链接</a>，<s class=\"cdx-strikethrough\">删除线</s>等。"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "B2Mm4njl-h", "type": "header", "data": {"text": "2.音视频支持", "level": 2}, "tunes": {"indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}}}, {"id": "vLAvqLJznY", "type": "paragraph", "data": {"text": "这是一段音频："}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "4VUeCwA1Zi", "type": "audio", "data": {"url": "http://cdn.dooring.cn/llYlOJNTYNX8Ahwt8AKU3VjilfV3"}, "tunes": {"indentTune": {"indentLevel": 0}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "N83WRm3q2Y", "type": "paragraph", "data": {"text": "大家可以欣赏一下， 接下来是一段视频："}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "Kz9oH7dBNt", "type": "video", "data": {"url": "<iframe src=\"//player.bilibili.com/player.html?isOutside=true&aid=1205613504&bvid=BV1vf421D7ec&cid=1575957377&p=1\" scrolling=\"no\" border=\"0\" frameborder=\"no\" framespacing=\"0\" allowfullscreen=\"true\"></iframe>"}, "tunes": {"indentTune": {"indentLevel": 0}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "YpRwP6jbyT", "type": "paragraph", "data": {"text": "这对于文档的展现力来说还是非常不错的。"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "Tpx23fv5OJ", "type": "header", "data": {"text": "3.基础的表格支持", "level": 2}, "tunes": {"indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}}}, {"id": "heaC5np25W", "type": "paragraph", "data": {"text": "下面是一段工作表格："}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "ocb475bETB", "type": "table", "data": {"withHeadings": true, "stretched": false, "content": [["任务", "描述", "负责人"], ["开发文件上传功能", "mix支持文件上传", "<mark class=\"cdx-marker___w6F33\" style=\"background-color: rgb(21, 33, 209);\"><span class=\"undefined\" style=\"color: rgb(246, 244, 244);\">徐小夕</span></mark>"], ["开发业务列表", "数据表格模块", "<mark class=\"cdx-marker___w6F33\" style=\"background-color: rgb(27, 177, 84);\"><span class=\"undefined\" style=\"color: rgb(240, 239, 239);\">王小蕾</span></mark>"], ["实现分页功能", "文档分页", "<mark class=\"cdx-marker___w6F33\" style=\"background-color: rgb(187, 125, 17);\"><span class=\"undefined\" style=\"color: rgb(253, 252, 252);\">高小姐</span></mark>"], ["单元测试", "整个系统的单元测试", "<mark class=\"cdx-marker___w6F33\" style=\"background-color: rgb(166, 64, 221);\"><span class=\"undefined\" style=\"color: rgb(248, 247, 247);\">湖湖</span></mark>"]]}, "tunes": {"indentTune": {"indentLevel": 0}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "YyWzRKCN9x", "type": "header", "data": {"text": "5.文章引用", "level": 2}, "tunes": {"indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}}}, {"id": "WrUlnVWb5_", "type": "quote", "data": {"text": "我是一个引用文本， 依然可以实现非常<b>富文本</b>的样式内容，如<code class=\"inline-code\">code</code>？", "alignment": "left"}, "tunes": {"indentTune": {"indentLevel": 0}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "tovMn5O2Of", "type": "paragraph", "data": {"text": "是时候测一下数学公式了："}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "0SI79xHcV1", "type": "math", "data": {"value": "x=y68+556\\tau\\left\\vert\\exponentialE\\right\\vert"}, "tunes": {"copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "fzBon91mFG", "type": "header", "data": {"text": "6.强大的代码块", "level": 2}, "tunes": {"indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}}}, {"id": "9BsoW_gPdy", "type": "paragraph", "data": {"text": "目前支持10多种语言代码的高亮，如下："}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "pXqvIyVTal", "type": "codeBox", "data": {"code": "<span class=\"hljs-keyword\"><span class=\"hljs-keyword\"><span class=\"hljs-keyword\">const</span></span></span> name = <span class=\"hljs-string\"><span class=\"hljs-string\"><span class=\"hljs-string\">'h5-dooring'</span></span></span>;<div><span class=\"hljs-keyword\"><span class=\"hljs-keyword\"><span class=\"hljs-keyword\">const</span></span></span> test = <span class=\"hljs-function\"><span class=\"hljs-params\"><span class=\"hljs-function\"><span class=\"hljs-params\"><span class=\"hljs-function\"><span class=\"hljs-params\">()</span></span></span></span></span><span class=\"hljs-function\"><span class=\"hljs-function\"> =&gt;</span></span></span> {</div><div>   <span class=\"hljs-keyword\"><span class=\"hljs-keyword\">return</span></span> <span class=\"hljs-number\"><span class=\"hljs-number\">666</span></span></div><div>}</div>", "language": "Auto-detect", "theme": "https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@9.18.1/build/styles/an-old-hope.min.css"}, "tunes": {"indentTune": {"indentLevel": 0}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "0Vqic8KPtf", "type": "header", "data": {"text": "7.支持PDF一键嵌入", "level": 2}, "tunes": {"indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}}}, {"id": "JG-FUBUXdv", "type": "PDF<PERSON><PERSON><PERSON>", "data": {"url": "https://orange.turntip.cn/uploads/busy-server/flowmix_docx多模态文档引擎解决方案介绍_193df6ac1d9.pdf"}, "tunes": {"indentTune": {"indentLevel": 0}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "HaRWxW9dc5", "type": "header", "data": {"text": "8.支持在线白板", "level": 2}, "tunes": {"indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}}}, {"id": "kphagJcPcX", "type": "board", "data": {"id": "wep_401723088918770"}, "tunes": {"indentTune": {"indentLevel": 0}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "wMOn9x_gxX", "type": "header", "data": {"text": "9.自定义图表组件", "level": 2}, "tunes": {"indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}}}, {"id": "Iwtru4Bmv1", "type": "chart", "data": {"type": "bar", "labels": ["周一", "周二", "周三", "周四"], "datasets": [{"data": [12, 19, 3, 5], "backgroundColor": ["rgba(255, 99, 132, 0.2)", "rgba(54, 162, 235, 0.2)", "rgba(255, 206, 86, 0.2)", "rgba(75, 192, 192, 0.2)"], "label": "flowmix/docx图表案例", "borderColor": ["rgba(255, 99, 132, 0.2)", "rgba(54, 162, 235, 0.2)", "rgba(255, 206, 86, 0.2)", "rgba(75, 192, 192, 0.2)"], "borderWidth": 1}]}, "tunes": {"indentTune": {"indentLevel": 0}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "mZ4q_4GGEP", "type": "header", "data": {"text": "10.自定义分页", "level": 2}, "tunes": {"indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}}}, {"id": "xCXcWRhGFm", "type": "paragraph", "data": {"text": "这里我直接分页啦， 为了能提供大文档高速打开!"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "jnPOJRLqgT", "type": "pageLine", "data": {"color": ""}, "tunes": {"indentTune": {"indentLevel": 0}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "nnAuJ6LnHG", "type": "paragraph", "data": {"text": "当然还有很多能力等待大家探索。"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "cZ1qfgXyxj", "type": "paragraph", "data": {"text": "如果你也想让自己的产品拥有一套强大的文档引擎， 欢迎找我私有化部署~"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "ZwCKxLzyBc", "type": "paragraph", "data": {"text": "私有化部署：<a href=\"/docx/price\" target=\"_blank\">点我查看</a>"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}, {"id": "ds3Khi8N0f", "type": "paragraph", "data": {"text": "<div><br></div><div><br></div><div><br></div>"}, "tunes": {"textVariant": "", "indentTune": {"indentLevel": 0}, "anyTuneName": {"alignment": "left"}, "copyBlockTune": {"prop": "value"}, "copyBlockLinkTune": {"prop": "value"}}}], "title": "flowmix/docx文档引擎案例"}