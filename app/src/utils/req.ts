import axios from "axios"
import { Message } from "@arco-design/web-vue"

interface HTTP_RESPONSE {
    success: 1 | 0
    data: any
    msg: string
}

const instance = axios.create({
    baseURL: process.env.BASE_API_URL,
    timeout: 60000,
})

instance.interceptors.request.use(
    function (config: any) {
        config.headers = {
            'x-requested-with': 'demo',
        }
        return config
    },
    function (error) {
        return Promise.reject(error)
    }
)

instance.interceptors.response.use(
    function (response) {
        // 返回完整的响应数据，让调用方处理
        return response.data
    },
    function (error) {
        console.error('API请求错误:', error)
        
        if (error && error.response) {
            const { status, data } = error.response
            console.error(`HTTP ${status}:`, data)
            
            switch (status) {
                case 400:
                    Message.error(data.message || '请求参数错误')
                    break
                case 401:
                    Message.error('未授权访问')
                    break
                case 403:
                    Message.error('禁止访问')
                    break
                case 404:
                    Message.error('请求的资源不存在')
                    break
                case 500:
                case 503:
                    Message.error(data.message || '服务器内部错误')
                    break
                case 501:
                    // 没有权限
                    window && (location.href = "/user/forbin")
                    break
                default:
                    Message.error(`请求失败 (${status})`)
            }
        } else if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
            Message.error('网络连接失败，请检查网络设置')
        } else if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
            Message.error('请求超时，请稍后重试')
        } else {
            Message.error('请求失败：' + (error.message || '未知错误'))
        }
        
        return Promise.reject(error)
    }
)

export const request = instance
export default instance

