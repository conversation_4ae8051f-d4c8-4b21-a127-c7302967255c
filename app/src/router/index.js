import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/:pathMatch(.*)*',
      name: 'notion',
      component: () => import('@/views/notion-page/notion-page.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/login.vue'),
    },
  ]
})

router.beforeEach((to, from, next) => {
  const isAuthenticated = localStorage.getItem('uid');

  // 如果已认证且访问登录页，重定向到首页
  if (isAuthenticated && to.name === 'Login') {
    next({ path: '/' });
  }
  // 如果未认证且访问需要认证的页面，重定向到登录页
  else if (!isAuthenticated && to.meta.requiresAuth) {
    next({ name: 'Login' });
  }
  // 其他情况允许访问
  else {
    next();
  }
});

export default router
