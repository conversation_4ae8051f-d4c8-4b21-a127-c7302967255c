import { request } from '@/utils/req'

// 文档API接口
export const documentApi = {
  // 获取文档列表
  getDocuments: () => {
    return request.get('/documents')
  },

  // 创建新文档
  createDocument: (name: string) => {
    return request.post('/documents', { name })
  },

  // 删除文档
  deleteDocument: (id: string) => {
    return request.delete(`/documents/${id}`)
  },

  // 重命名文档
  renameDocument: (id: string, name: string) => {
    return request.put(`/documents/${id}`, { name })
  }
}