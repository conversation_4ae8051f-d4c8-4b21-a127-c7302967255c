/* eslint-disable react-hooks/rules-of-hooks */
import { Editor as CoreEditor } from '../core'
import {
  customRef,
  markRaw,
} from 'vue'

// 创建一个防抖的响应式引用，延迟视图更新以优化性能
function useDebouncedRef(value) {
  return customRef((track, trigger) => {
    return {
      get() {
        track()
        return value
      },
      set(newValue) {
        // update state
        value = newValue

        // update view as soon as possible
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            trigger()
          })
        })
      },
    }
  })
}

export class Editor extends CoreEditor {
  contentComponent = null

  appContext = null

  constructor(options = {}) {
    // 初始化编辑器实例并设置响应式状态
    super(options)

    this.reactiveState = useDebouncedRef(this.view.state)
    this.reactiveExtensionStorage = useDebouncedRef(this.extensionStorage)

    this.on('beforeTransaction', ({ nextState }) => {
      this.reactiveState.value = nextState
      this.reactiveExtensionStorage.value = this.extensionStorage
    })

    return markRaw(this) // eslint-disable-line
  }
  // 提供响应式的状态访问
  get state() {
    return this.reactiveState ? this.reactiveState.value : this.view.state
  }
  // 提供响应式的存储访问
  get storage() {
    return this.reactiveExtensionStorage ? this.reactiveExtensionStorage.value : super.storage
  }

  /**
   * 注册 ProseMirror 插件并更新响应式状态
   */
  registerPlugin(
    plugin,
    handlePlugins,
  ) {
    const nextState = super.registerPlugin(plugin, handlePlugins)

    if (this.reactiveState) {
      this.reactiveState.value = nextState
    }

    return nextState
  }

  /**
   * 注销 ProseMirror 插件并更新响应式状态
   */
  unregisterPlugin(nameOrPluginKey) {
    const nextState = super.unregisterPlugin(nameOrPluginKey)

    if (this.reactiveState && nextState) {
      this.reactiveState.value = nextState
    }

    return nextState
  }
}
