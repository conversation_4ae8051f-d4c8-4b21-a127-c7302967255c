@use "../config" as *;

.#{$prefix}-color-picker {
  position: relative;

  &__title {
    font-size: 0.85em;
    font-weight: 500;
    color: var(--#{$prefix}-text-color-3);
    margin-top: 0.7rem;
    margin-bottom: 0.4rem;
  }

  &__default {
    width: 100%;
    height: 28px;
    font-size: 0.85em;
    border-radius: 0.3rem;
    border: 1px solid var(--#{$prefix}-border-color);
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-bottom: 0.7rem;
    color: var(--#{$prefix}-text-color-1);

    &:hover {
      color: var(--#{$prefix}-text-color);
      background-color: rgba(var(--#{$prefix}-border-color-val), 0.5);
    }
  }

  &__base,
  &__standard,
  &__recent {
    display: grid;
    grid-template-columns: repeat(9, 20px);
    grid-auto-flow: column;
    grid-template-rows: repeat(6, 20px);
    gap: 0.35rem;

    &-item {
      width: 20px;
      height: 20px;
      border-radius: 0.3rem;
      cursor: pointer;
      transition: transform 0.2s ease;
      border: 1px solid rgba(var(--#{$prefix}-border-color-val), 0.5);
      box-sizing: border-box;

      &:hover {
        transform: scale(1.15);
      }
    }
  }

  &__standard,
  &__recent {
    display: grid;
    grid-auto-flow: row;
    grid-template-columns: repeat(9, 20px);
    grid-template-rows: repeat(1, 20px);
    gap: 0.35rem;
  }
}
