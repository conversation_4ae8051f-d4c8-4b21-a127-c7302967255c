@use "../config" as *;

.#{$prefix}-button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  border-radius: 0.375rem;
  border: none;
  background-color: transparent;
  box-sizing: border-box;
  cursor: pointer;
  color: var(--#{$prefix}-text-color-1);
  padding: 0 8px;
  box-sizing: border-box;

  &.active {
    background-color: rgba(var(--#{$prefix}-border-color-val), 0.5);
    color: var(--#{$prefix}-theme-primary) !important;
  }
  &.semi-active {
    background-color: rgba(var(--#{$prefix}-border-color-val), 0.5);
  }
  &.danger {
    color: var(--#{$prefix}-color-red) !important;
  }
  &.success {
    color: var(--#{$prefix}-color-green) !important;
  }
  &.disabled {
    color: var(--#{$prefix}-border-color) !important;
    background-color: transparent !important;
    cursor: not-allowed;
  }
  &.long {
    width: 100%;
    align-items: center;
    justify-content: flex-start;
  }

  &:hover {
    background-color: rgba(var(--#{$prefix}-border-color-val), 0.5);
    color: var(--#{$prefix}-text-color);
  }
}
