@use "config" as *;

// _iframe.scss
.#{$prefix}-iframe {
  // 主容器样式
  &-wrapper {
    position: relative;
    margin: 15px 0;
    border: 1px solid #eee;
    border-radius: 4px;
    overflow: hidden;

    // 编辑按钮
    &-edit-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      background-color: rgba(0,0,0,0.6);
      color: white;
      border: none;
      border-radius: 4px;
      padding: 5px 10px;
      cursor: pointer;
      z-index: 10;
      opacity: 0;
      transition: opacity 0.2s;

      &:hover {
        background-color: rgba(0,0,0,0.8);
      }
    }

    &:hover {
      .px-charts-iframe-edit-btn {
        opacity: 1;
      }
    }
  }

  // 对话框样式
  &-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;

    // 对话框内容
    &-content {
      background-color: white;
      border-radius: 8px;
      width: 500px;
      max-width: 90%;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);

      // 对话框头部
      h3 {
        margin: 0;
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        font-size: 16px;
        font-weight: 500;
      }

      // 对话框主体
      &-body {
        padding: 20px;

        label {
          display: block;
          margin-bottom: 5px;
          font-size: 14px;
        }

        // URL输入框
        &-input {
          width: 100%;
          padding: 8px;
          margin: 10px 0;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;

          &:focus {
            outline: none;
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }

        // 预览区域
        &-preview {
          margin-top: 15px;

          h4 {
            margin: 0 0 10px;
            font-size: 14px;
            font-weight: 500;
          }

          iframe {
            border: 1px solid #eee;
            border-radius: 4px;
            width: 100%;
            height: 200px;
            background-color: #f9f9f9;
          }
        }
      }

      // 对话框底部按钮
      &-footer {
        padding: 15px 20px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
        gap: 10px;

        button {
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          transition: background-color 0.2s;
        }

        // 取消按钮
        &-cancel-btn {
          background-color: #f5f5f5;
          border: 1px solid #ddd;

          &:hover {
            background-color: #f0f0f0;
          }
        }

        // 确认按钮
        &-confirm-btn {
          background-color: #409eff;
          color: white;
          border: none;

          &:hover {
            background-color: #368ee0;
          }

          &:active {
            background-color: #2a7dcc;
          }
        }
      }
    }
  }
}
