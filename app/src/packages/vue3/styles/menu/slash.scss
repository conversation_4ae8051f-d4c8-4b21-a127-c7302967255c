@use "../config" as *;

.#{$prefix}-slash-menu {
  width: auto;
  height: auto;
  max-height: calc(50vh - 50px);
  padding: 0.3rem;
  border-radius: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.25rem;
  background-color: var(--#{$prefix}-bg-color);
  border: 1px solid var(--#{$prefix}-border-color);
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;

  // 隐藏滚动条
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;

  &__item {
    min-width: 200px;
    width: 100%;
    height: 34px;
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    font-weight: 500;
    padding: 0 0.375rem;
    border-radius: 0.375rem;
    color: var(--#{$prefix}-text-color-2);
    box-sizing: border-box;
    cursor: pointer;

    &:hover {
      background-color: rgba(var(--#{$prefix}-border-color-val), 0.5);
      color: var(--#{$prefix}-text-color);
    }

    &.active {
      background-color: rgba(var(--#{$prefix}-border-color-val), 0.5);
      color: var(--#{$prefix}-text-color);
    }

    &-left {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      &-title {
        font-size: 0.875rem;
        font-weight: 500;
      }
    }

    &-right {
      display: flex;
      align-items: center;
      justify-content: end;

      &-shortcutkeys {
        font-size: 0.65rem;
        display: flex;
        gap: 0.1rem;

        &-key {
          display: inline-flex;
          justify-content: center;
          align-items: center;
          border-radius: 0.2rem;
          box-sizing: border-box;
        }
      }
    }
  }
}
