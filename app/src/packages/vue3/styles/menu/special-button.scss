@use "../config" as *;

.#{$prefix}-special-button {
  &__icon-box {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.3rem;
    border: 1px solid var(--#{$prefix}-border-color);
    box-sizing: border-box;
  }

  &__text-box {
    max-width: 76px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.875rem;
  }

  &__link,
  &__color,
  &__background,
  &__text-align,
  &__font-family,
  &__font-size,
  &__table {
    width: auto;
    height: auto;
    padding: 0.2rem;
    border-radius: 0.5rem;
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    gap: 0.125rem;
    background-color: var(--#{$prefix}-bg-color);
    border: 1px solid var(--#{$prefix}-border-color);
    box-sizing: border-box;
  }

  &__link {
    &-input {
      width: 240px;
      height: 32px;
      border: none;
      outline: none;
      background-color: rgba(var(--#{$prefix}-border-color-val), 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 0.375rem;
      padding-left: 0.5rem;
      box-sizing: border-box;

      &-icon {
        margin-right: 0.5rem;
        color: var(--#{$prefix}-text-color-1);
      }

      &-inner {
        width: 100%;
        height: 100%;
        padding-right: 0.5rem;
        border: none;
        outline: none;
        background-color: transparent;
        box-sizing: border-box;

        &:focus {
          background-color: transparent;
        }
      }
    }
  }

  &__color,
  &__background {
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    &-title {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &.mt-2 {
        margin-top: 0.5rem;
      }

      &-text {
        font-size: 0.85em;
        font-weight: 500;
        color: var(--#{$prefix}-text-color-2);
      }
    }

    &-box {
      display: grid;
      grid-template-columns: repeat(5, 24px);
      grid-auto-rows: 24px;
      gap: 0.5rem;

      &-item {
        width: 100%;
        height: 100%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.375rem;
        border: 1px solid var(--#{$prefix}-border-color);
        box-sizing: border-box;

        &:hover {
          border-width: 2.5px;
          box-sizing: border-box;
        }

        svg {
          filter: drop-shadow(0 0 1px var(--#{$prefix}-shadow-color));
        }
      }
    }
  }

  &__font-family,
  &__font-size {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
  }

  &__table {
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    &-grid {
      display: grid;
      grid-template-columns: repeat(10, 20px);
      border: 1px solid var(--#{$prefix}-border-color);
      border-radius: 0.25rem;

      &-cell {
        cursor: pointer;
        width: 20px;
        height: 18.5px;
        border-right: 1px solid var(--#{$prefix}-border-color);
        border-bottom: 1px solid var(--#{$prefix}-border-color);

        &:nth-child(10n) {
          border-right: none;
        }

        &:nth-last-child(-n + 10) {
          border-bottom: none;
        }

        &.is-highlighted {
          background-color: rgba(var(--#{$prefix}-border-color-val), 0.4);
        }
      }
    }

    &-size-text {
      margin-top: 0.5rem;
      font-size: 12px;
      color: var(--#{$prefix}-text-color-2);
      text-align: center;
    }
  }
}
