@use 'config' as *;

.#{$prefix}.ProseMirror{
  .#{$prefix}__selection {
    position: relative;
    background-color: Highlight;
    background-color: AccentColor;

    display: inline; 

    line-height: inherit;

    padding-top: 0.05em;
    padding-bottom: 0.05em;
    // Add a little negative margin to compensate for the spacing caused by padding.
    margin-top: -0.05em;
    margin-bottom: -0.05em;
    
    // Ensure that the selected area spans the entire line
    box-decoration-break: clone;
    -webkit-box-decoration-break: clone;

    @media (prefers-color-scheme: dark) {
      background-color: SelectedItem;
    }
  }
}