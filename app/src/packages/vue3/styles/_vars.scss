@use "config" as *;

:root {
  --#{$prefix}-color-base: var(--#{$prefix}-color-black);
  --#{$prefix}-color-white: rgba(253, 253, 253, 1);
  --#{$prefix}-color-black: rgba(3, 3, 5, 1);
  --#{$prefix}-color-purple: #ae3ec9;
  --#{$prefix}-color-red: #f03e3e;
  --#{$prefix}-color-yellow: #f59f00;
  --#{$prefix}-color-blue: #3b82f6;
  --#{$prefix}-color-blue-val: 59, 130, 246;
  --#{$prefix}-color-green: #37b24d;
  --#{$prefix}-color-orange: #f76707;
  --#{$prefix}-color-pink: #d6336c;
  --#{$prefix}-color-gray: #787774;
  --#{$prefix}-color-brown: #9f6b53;

  --#{$prefix}-bg-base: var(--#{$prefix}-color-white);
  --#{$prefix}-bg-white: var(--#{$prefix}-color-white);
  --#{$prefix}-bg-black: var(--#{$prefix}-color-black);
  --#{$prefix}-bg-purple: rgba(246, 243, 248, 1);
  --#{$prefix}-bg-red: rgba(253, 235, 235, 1);
  --#{$prefix}-bg-yellow: rgba(251, 243, 219, 1);
  --#{$prefix}-bg-blue: rgba(231, 243, 248, 1);
  --#{$prefix}-bg-green: rgba(237, 243, 236, 1);
  --#{$prefix}-bg-orange: rgba(250, 235, 221, 1);
  --#{$prefix}-bg-pink: rgba(250, 241, 245, 1);
  --#{$prefix}-bg-gray: rgba(241, 241, 239, 1);
  --#{$prefix}-bg-brown: rgba(244, 238, 238, 1);

  --#{$prefix}-theme-primary: var(--#{$prefix}-color-blue);
  --#{$prefix}-theme-primary-val: var(--#{$prefix}-color-blue-val);

  --#{$prefix}-border-color-val: 228, 228, 231;
  --#{$prefix}-border-color: rgba(228, 228, 231, 1);
  --#{$prefix}-border-color-inverse: rgba(39, 39, 42, 1);

  --#{$prefix}-text-color: var(--#{$prefix}-color-black);
  --#{$prefix}-text-color-1: rgba(51, 51, 51, 1);
  --#{$prefix}-text-color-2: rgba(113, 113, 122, 1);
  --#{$prefix}-text-color-3: rgba(135, 135, 134, 1);
  --#{$prefix}-text-color-inverse: var(--#{$prefix}-color-white);

  --#{$prefix}-bg-color: var(--#{$prefix}-color-white);
  --#{$prefix}-bg-color-1: rgba(247, 247, 247, 1);
  --#{$prefix}-bg-color-2: rgba(239, 239, 239, 1);
  --#{$prefix}-bg-color-inverse: var(--#{$prefix}-color-black);
  --#{$prefix}-bg-color-inverse-1: rgba(39, 39, 42, 1);
  --#{$prefix}-bg-color-inverse-2: rgba(55, 55, 65, 1);

  --#{$prefix}-shadow-color: rgba(0, 0, 0, 0.5);

  --#{$prefix}-font-size: 1rem;
  --#{$prefix}-line-height: 1.5;
  --#{$prefix}-margin: 1.2rem;
  --#{$prefix}-margin-mini: 0.5rem;
}

[isle-theme="dark"] {
  --#{$prefix}-color-base: var(--#{$prefix}-color-white);
  --#{$prefix}-color-purple: #9d68d3;
  --#{$prefix}-color-red: #df5452;
  --#{$prefix}-color-yellow: #ca9849;
  --#{$prefix}-color-blue: #60a5fa;
  --#{$prefix}-color-blue-val: 96, 165, 250;
  --#{$prefix}-color-green: #529e72;
  --#{$prefix}-color-orange: #c77d48;
  --#{$prefix}-color-pink: #ba4081;
  --#{$prefix}-color-gray: #a8a29e;
  --#{$prefix}-color-brown: #ba856f;

  --#{$prefix}-bg-base: var(--#{$prefix}-color-black);
  --#{$prefix}-bg-white: var(--#{$prefix}-color-white);
  --#{$prefix}-bg-black: var(--#{$prefix}-color-black);
  --#{$prefix}-bg-purple: rgba(60, 45, 73, 1);
  --#{$prefix}-bg-red: rgba(82, 46, 42, 1);
  --#{$prefix}-bg-yellow: rgba(86, 67, 40, 1);
  --#{$prefix}-bg-blue: rgba(20, 58, 78, 1);
  --#{$prefix}-bg-green: rgba(36, 61, 48, 1);
  --#{$prefix}-bg-orange: rgba(92, 59, 35, 1);
  --#{$prefix}-bg-pink: rgba(78, 44, 60, 1);
  --#{$prefix}-bg-gray: rgba(47, 47, 47, 1);
  --#{$prefix}-bg-brown: rgba(74, 50, 40, 1);

  --#{$prefix}-border-color-val: 39, 39, 42;
  --#{$prefix}-border-color: rgba(39, 39, 42, 1);
  --#{$prefix}-border-color-inverse: rgba(229, 229, 229, 1);

  --#{$prefix}-text-color: var(--#{$prefix}-color-white);
  --#{$prefix}-text-color-1: rgba(229, 229, 229, 1);
  --#{$prefix}-text-color-2: rgba(212, 212, 212, 1);
  --#{$prefix}-text-color-3: rgba(161, 161, 170, 1);
  --#{$prefix}-text-color-inverse: var(--#{$prefix}-color-black);

  --#{$prefix}-bg-color: var(--#{$prefix}-color-black);
  --#{$prefix}-bg-color-1: rgba(39, 39, 42, 1);
  --#{$prefix}-bg-color-2: rgba(55, 55, 65, 1);
  --#{$prefix}-bg-color-inverse: var(--#{$prefix}-color-white);
  --#{$prefix}-bg-color-inverse-1: rgba(247, 247, 247, 1);
  --#{$prefix}-bg-color-inverse-2: rgba(239, 239, 239, 1);

  --#{$prefix}-shadow-color: rgba(255, 255, 255, 0.5);
}
