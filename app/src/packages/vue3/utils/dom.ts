/**
 * 创建元素工具函数
 *
 * @param  {string} tagName           - new Element tag name
 * @param  {Array|string} classNames  - list or name of CSS class
 * @param  {object} attributes        - any attributes
 * @returns {Element}
 */
export function make(tagName: keyof HTMLElementTagNameMap, classNames?: string | null | string[], attributes: any = {}) {
  const el = document.createElement(tagName);

  if (Array.isArray(classNames)) {
    el.classList.add(...classNames as []);
  } else if (classNames) {
    el.classList.add(classNames);
  }

  for (const attrName in attributes) {
    // @ts-ignore
    el[attrName] = attributes[attrName];
  }

  return el;
};

export const hideElementById = (id: string) => {
  // 隐藏滑块
  const block = document.querySelector(`#${id}`) as HTMLElement;
  block.style.display = 'none';
  return
}

export function makeFragment(htmlString: string) {
  const tempDiv = document.createElement('div');

  tempDiv.innerHTML = htmlString.trim();

  const fragment = document.createDocumentFragment();

  fragment.append(...Array.from(tempDiv.childNodes));

  return fragment;
}

export function findElement(target: Element, searchNodeClass: string[]) {
  return searchNodeClass.some(name => !!target.querySelector(`.${name}`))
}

// 复制内容到剪切板
export function copyToClipboard(text: string) {
  return new Promise((resolve, reject) => {
    // 检查是否支持 navigator.clipboard API
    if (navigator.clipboard && typeof navigator.clipboard.writeText === "function") {
      navigator.clipboard.writeText(text)
          .then(() => resolve(true))
          .catch(err => reject(err));
    } else {
      // 回退方案：创建一个临时的 textarea 元素
      const textArea = document.createElement("textarea");
      textArea.value = text;

      // 将 textarea 设置为不可见
      textArea.style.position = "fixed";
      textArea.style.top = "-9999px";
      textArea.style.left = "-9999px";

      document.body.appendChild(textArea);

      // 选中并复制文本
      textArea.focus();
      textArea.select();

      try {
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        if (successful) {
          resolve(true);
        } else {
          reject(new Error('Unable to copy text'));
        }
      } catch (err) {
        document.body.removeChild(textArea);
        reject(err);
      }
    }
  });
}
