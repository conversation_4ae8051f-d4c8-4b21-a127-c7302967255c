import { defineComponent, h } from "vue";
import { ITooltip, IButton, IIcon } from "../ui";
import { t } from "../../../core";
import { make } from "../../utils/dom";

export default defineComponent({
  name: "ButtonI<PERSON>",
  props: {
    editor: {
      type: Object,
      required: true,
    },
    menu: {
      type: Object,
      required: true,
    },
    uploadFn: {
      type: Function,
      default: () => null,
    }
  },
  setup(props) {
    const { menu, editor, uploadFn } = props;
    return () => h(
      ITooltip,
      { text: t(menu.name), shortcutkeys: menu.shortcutkeys },
      {
        default: () =>
          h(
            IButton,
            {
              active:
                menu?.isActive &&
                menu?.isActive({ editor: editor }),
              disabled:
                menu?.isDisabled &&
                menu?.isDisabled({ editor: editor }),
              onClick: () => {
                const fileDom = make('input', '', {type: 'file'});
                fileDom.style.display = 'none';
                document.body.append(fileDom);
                fileDom.click();
                fileDom.addEventListener('change', async (event) => {
                  // 处理文件选择事件
                  const file = event.target.files[0];
                  if (file) {
                    if(uploadFn) {
                      uploadFn(file, (url) => {
                        menu.command({ editor: props.editor, url })
                      })
                      return
                    }
                    // 生成临时 URL
                    const url = URL.createObjectURL(file);
                    menu.command({ editor: props.editor, url })
                  }

                });
              },
            },
            {
              icon: () =>
                h(IIcon, {
                  name: menu.name,
                  size: 14,
                }),
            },
          ),
      },
    );
  },
});
