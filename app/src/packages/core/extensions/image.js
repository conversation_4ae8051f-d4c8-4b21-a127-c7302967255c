import Image from "@tiptap/extension-image";
import {prefixClass} from "../utils/prefix.js";

export default Image.extend({
  addOptions() {
    return {
      ...this.parent?.(),
      name: "image",
      desc: "",
      command: ({ editor, url }) => {
        if (url) {
          editor.chain().focus().setImage({ src: url }).run()
         // 记录当前选区位置，用于插入后定位
          const { to } = editor.state.selection;

          // 执行插入图片命令
          editor.chain().focus().setImage({ src: url }).run();

          // 在图片后插入空白文本段落
          editor.chain()
            .focus(to + 1) // 定位到图片后面
            .insertContent([
              {
                type: 'paragraph',
                content: [
                  {
                    type: 'text',
                    text: '', // 空白文本
                  },
                ],
              },
            ])
            .run();
        }
      },
      isActive: ({ editor }) => editor.isActive("image"),
      // isDisabled: ({ editor }) => !editor.can().toggleImage(),
      shortcutkeys: "Mod-M",
      HTMLAttributes: {
        class: `${prefixClass}__image`,
      },
      parseHTML() {
        return [{ tag: 'img' }]
      },
    };
  },
});
