import { Node, mergeAttributes } from '@tiptap/core'
import modal from '../plugins/modal'

const Iframe_v0 = Node.create({
  name: 'iframe',
  content: 'inline*',
  group: 'block',
  atom: true,
  inline: false,

  // 关键：声明src属性，确保Tiptap正确处理
    addAttributes() {
      return {
        src: {
          default: '',
          parseHTML: (element) => {
            // 同时处理直接iframe和包裹div两种情况
            const iframe = element.tagName === 'IFRAME'
              ? element
              : element.querySelector('iframe');
            return iframe ? iframe.getAttribute('src') || '' : '';
          },
          renderHTML: (attrs) => ({ src: attrs.src })
        },
        width: {
          default: '100%',
          parseHTML: (element) => {
            const iframe = element.tagName === 'IFRAME'
              ? element
              : element.querySelector('iframe');
            return iframe ? iframe.getAttribute('width') || '100%' : '100%';
          }
        },
        height: {
          default: '400px',
          parseHTML: (element) => {
            const iframe = element.tagName === 'IFRAME'
              ? element
              : element.querySelector('iframe');
            return iframe ? iframe.getAttribute('height') || '400px' : '400px';
          }
        },
        // 其他需要保留的属性
        frameborder: {
          default: '0',
          renderHTML: () => ({ frameborder: '0' })
        },
        allowfullscreen: {
          default: 'true',
          renderHTML: () => ({ allowfullscreen: 'true' })
        },
        sandbox: {
          default: 'allow-same-origin allow-scripts',
          renderHTML: () => ({ sandbox: 'allow-same-origin allow-scripts' })
        }
      };
    },

  addOptions() {
    return {
      HTMLAttributes: {},
      allowedHosts: [],
      command: ({ editor }) => {
        editor.chain().focus().setIframe().run()
      }
    }
  },

    parseHTML() {
      return [
        // 情况1：直接解析iframe标签
        {
          tag: 'iframe',
          getAttrs: (dom) => ({
            src: dom.getAttribute('src') || '',
            width: dom.getAttribute('width') || '100%',
            height: dom.getAttribute('height') || '400px'
          })
        },
        // 情况2：解析带包裹层的结构
        {
          tag: 'div[data-iframe]',
          getAttrs: (dom) => {
            const iframe = dom.querySelector('iframe');
            return iframe ? {
              src: iframe.getAttribute('src') || '',
              width: iframe.getAttribute('width') || '100%',
              height: iframe.getAttribute('height') || '400px'
            } : false;
          }
        }
      ];
    },

  renderHTML({ HTMLAttributes }) {
    // 关键：使用与parseHTML匹配的结构
    return [
      'div',
      {
        'data-iframe': 'true',
        class: 'iframe-wrapper'
      },
      [
        'iframe',
        mergeAttributes(
          {
            src: HTMLAttributes.src || 'about:blank',
            width: HTMLAttributes.width || '100%',
            height: HTMLAttributes.height || '400px',
            frameborder: '0',
            allowfullscreen: 'true',
            sandbox: 'allow-same-origin allow-scripts',
            style: 'display:block;'
          }
        )
      ]
    ];
  },

  // 简化命令实现，使用options中的command
    addCommands() {
      return {
        setIframe: () => ({ commands, editor }) => {

          // 保存当前节点名称（解决 this 指向问题）
          const nodeName = this.name;
          const modalId = `iframe_modal_${Date.now()}`;

          const content = `
          <div class="cm-alert">
            <span class="cm-alert-icon">⚠</span>
            <span class="cm-alert-content">因目标网页的限制，部分网页可能无法成功预览。</span>
          </div>
          <div class="cm-form-item">
            <div class="cm-form-label">
              <span class="cm-form-required">*</span>
              <span>网页地址</span>
            </div>
            <input type="text" class="cm-form-input" placeholder="请输入" id="${modalId}">
          </div>
        `;

          // 打开模态框
          modal.open(
            "插入网页",
            content,
            () => { /* 取消回调，无需处理 */ },
            () => {
              // 确认回调：获取输入值
              const input = document.getElementById(modalId);
              if (!input || !input.value.trim()) return false;

              // 获取光标位置
              const { from } = editor.state.selection;

              // 插入节点（使用保存的 nodeName 避免 this 指向问题）
              editor.commands.insertContentAt(from, {
                type: nodeName,
                attrs: { src: input.value.trim() }
              });
            }
          );

          // 老版本 Tiptap 命令可以返回 true 表示执行成功
          return true;
        },
        // 添加编辑命令
        editIframe: (src) => ({ editor }) => {
          const nodeName = this.name;
          const modalId = `iframe_modal_${Date.now()}`;

          const content = `
          <div class="cm-alert">
            <span class="cm-alert-icon">⚠</span>
            <span class="cm-alert-content">因目标网页的限制，部分网页可能无法成功预览。</span>
          </div>
          <div class="cm-form-item">
            <div class="cm-form-label">
              <span class="cm-form-required">*</span>
              <span>网页地址</span>
            </div>
            <input type="text" class="cm-form-input" placeholder="请输入" id="${modalId}" value="${src}">
          </div>
        `;

          modal.open(
            "编辑网页",
            content,
            () => {},
            () => {
              const input = document.getElementById(modalId);
              if (!input || !input.value.trim()) return;

              // 更新当前iframe的属性
              if (editor.isActive(nodeName)) {
                editor.commands.updateAttributes(nodeName, { src: input.value.trim() });
              }
            }
          );

          return true;
        },
      };
    },

  addNodeView() {
    return ({ editor, node, getPos }) => {
      // 外层容器必须与renderHTML返回的结构匹配
      const wrapper = document.createElement('div');
      wrapper.setAttribute('data-iframe', 'true');
      wrapper.className = 'iframe-wrapper';

      const iframe = document.createElement('iframe');
      iframe.src = node.attrs.src || 'about:blank';
      iframe.width = node.attrs.width || '100%';
      iframe.height = node.attrs.height || '400px';
      iframe.frameBorder = '0';
      iframe.allowFullscreen = true;
      iframe.sandbox = 'allow-same-origin allow-scripts';
      iframe.style.display = 'block';

      const editButton = document.createElement('button');
      editButton.className = 'iframe-edit-btn';
      editButton.innerHTML = '<i class="fas fa-edit"></i> 编辑';
      editButton.addEventListener('click', () => {
        editor.commands.editIframe(node.attrs.src);
      });

      wrapper.appendChild(iframe);
      wrapper.appendChild(editButton);

      return {
        dom: wrapper,
        update: (updatedNode) => {
          if (updatedNode.type.name !== 'iframe') return false;

          // 更新所有属性
          if (iframe.src !== updatedNode.attrs.src) {
            iframe.src = updatedNode.attrs.src;
          }
          if (iframe.width !== updatedNode.attrs.width) {
            iframe.width = updatedNode.attrs.width;
          }
          if (iframe.height !== updatedNode.attrs.height) {
            iframe.height = updatedNode.attrs.height;
          }

          return true;
        }
      };
    }
  }
})

export default Iframe_v0
