import { Node, mergeAttributes } from '@tiptap/core'
// import { Plugin, Plugin<PERSON>ey } from 'prosemirror-state'

// import './iframe.css'

const Iframe = Node.create({
  name: "iframe",
  draggable: true,
  inline: false,
  group: "block",

  addOptions() {
    return {
      HTMLAttributes: { frameborder: 0 },
      command: ({ editor }) => {
        editor.chain().focus().setIframe({
          src: 'https://dooring.vip'
        }).run()
      }
    };
  },

  addAttributes() {
    return {
      src: { default: null },
      HTMLAttributes: {
        default: null,
        renderHTML: (attributes) => {
          return attributes.HTMLAttributes || {};
        },
      },
    };
  },

  parseHTML() {
    return [{ tag: "iframe" }];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "iframe",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
    ];
  },

  addCommands() {
    return {
      setIframe:
        (options) =>
          ({ commands }) => {
            return commands.insertContent({
              type: this.name,
              attrs: options,
            });
          },
    };
  },
});


export default Iframe;
