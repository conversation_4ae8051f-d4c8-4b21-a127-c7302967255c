import './index.css'
class CustomModal {
    constructor() {
        this.modal = null;
        this.overlay = null;
        this.closeBtn = null;
        this.cancelBtn = null;
        this.confirmBtn = null;
        this.content = null;
        this.titleElement = null;
        this.onCancel = null;
        this.onConfirm = null;
    }

    create() {
        // Create modal elements
        this.overlay = document.createElement('div');
        this.overlay.className = 'cm-overlay';

        this.modal = document.createElement('div');
        this.modal.className = 'cm-modal';

        // Create title container
        this.titleElement = document.createElement('div');
        this.titleElement.className = 'cm-title';

        this.closeBtn = document.createElement('span');
        this.closeBtn.className = 'cm-close';
        this.closeBtn.innerHTML = '×';

        this.content = document.createElement('div');
        this.content.className = 'cm-content';

        // Create button container
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'cm-button-container';

        this.cancelBtn = document.createElement('button');
        this.cancelBtn.className = 'cm-button cm-cancel';
        this.cancelBtn.textContent = '取消';

        this.confirmBtn = document.createElement('button');
        this.confirmBtn.className = 'cm-button cm-confirm';
        this.confirmBtn.textContent = '确定';

        // Assemble modal
        this.titleElement.appendChild(document.createTextNode(''));
        this.titleElement.appendChild(this.closeBtn);

        buttonContainer.appendChild(this.cancelBtn);
        buttonContainer.appendChild(this.confirmBtn);

        this.modal.appendChild(this.titleElement);
        this.modal.appendChild(this.content);
        this.modal.appendChild(buttonContainer);

        this.overlay.appendChild(this.modal);

        // Add event listeners
        this.closeBtn.addEventListener('click', () => this.close());
        this.cancelBtn.addEventListener('click', () => this.cancel());
        this.confirmBtn.addEventListener('click', () => this.confirm());
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) this.close();
        });

        // Append to body
        document.body.appendChild(this.overlay);
    }

    open(title, content, onCancel, onConfirm) {
        if (!this.modal) {
            this.create();
        }

        // Update title and content
        this.titleElement.firstChild.textContent = title;
        this.content.innerHTML = content;
        this.onCancel = onCancel;
        this.onConfirm = onConfirm;

        this.overlay.style.display = 'flex';

        // Focus confirm button by default
        this.confirmBtn.focus();
    }

    close() {
        if (this.overlay) {
            this.overlay.style.display = 'none';
        }
    }

    cancel() {
        if (typeof this.onCancel === 'function') {
            this.onCancel();
        }
        this.close();
    }

    confirm() {
        if (typeof this.onConfirm === 'function') {
            this.onConfirm();
        }
        this.close();
    }
}

export default new CustomModal();