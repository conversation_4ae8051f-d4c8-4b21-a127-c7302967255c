.cm-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.45);
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.cm-modal {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    position: relative;
    width: 90%;
    max-width: 520px;
    display: flex;
    flex-direction: column;
}

.cm-title {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 16px;
    font-weight: 500;
    color: #000;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cm-close {
    font-size: 22px;
    color: #999;
    cursor: pointer;
    line-height: 1;
    transition: color 0.2s;
}

.cm-close:hover {
    color: #444;
}

.cm-content {
    padding: 24px;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
}

.cm-button-container {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.cm-button {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: opacity 0.2s;
    border: none;
}

.cm-button:hover {
    opacity: 0.85;
}

.cm-cancel {
    background-color: #fff;
    border: 1px solid #ddd;
    color: #666;
}

.cm-confirm {
    background-color: #1677ff;
    color: white;
}

/* Alert styles */
.cm-alert {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 8px 12px;
    background-color: #e6f4ff;
    border-radius: 4px;
    margin-bottom: 16px;
}

.cm-alert-icon {
    color: #1677ff;
    font-size: 14px;
}

.cm-alert-content {
    color: #666;
    font-size: 14px;
}

/* Form styles */
.cm-form-item {
    margin-bottom: 16px;
}

.cm-form-label {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.cm-form-required {
    color: #ff4d4f;
    margin-right: 4px;
}

.cm-form-input {
    width: calc(100% - 32px);
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s;
}

.cm-form-input:focus {
    border-color: #1677ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}