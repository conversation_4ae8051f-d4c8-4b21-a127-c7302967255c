<template>
  <div class="version-manager">
    <!-- 版本管理面板 -->
    <a-drawer
      :visible="visible"
      title="版本历史"
      width="480"
      placement="right"
      @cancel="handleClose"
    >
      <template #title>
        <div class="flex items-center justify-between">
          <span>版本历史</span>
          <a-button 
            type="primary" 
            size="small"
            @click="handleCreateVersion"
            :loading="createLoading"
          >
            保存当前版本
          </a-button>
        </div>
      </template>

      <!-- 版本列表 -->
      <div class="version-list">
        <a-spin :loading="loading">
          <div v-if="versions.length === 0" class="empty-state">
            <a-empty description="暂无版本历史" />
          </div>
          
          <div v-else class="space-y-3">
            <div
              v-for="version in versions"
              :key="version.id"
              class="version-item p-3 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
              :class="{ 'border-blue-500 bg-blue-50': selectedVersion?.id === version.id }"
            >
              <!-- 版本头部信息 -->
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <span class="font-medium text-sm">{{ version.title }}</span>
                  <a-tag 
                    v-if="version.isAutoSave" 
                    color="green" 
                    size="small"
                  >
                    自动保存
                  </a-tag>
                </div>
                
                <a-dropdown>
                  <a-button type="text" size="small">
                    <template #icon>
                      <span v-html="icon['more'].body"></span>
                    </template>
                  </a-button>
                  <template #content>
                    <a-doption @click="handleRestoreVersion(version)">
                      <span v-html="icon['restore'].body" class="mr-2"></span>
                      恢复此版本
                    </a-doption>
                    <a-doption @click="handleEditVersion(version)">
                      <span v-html="icon['edit'].body" class="mr-2"></span>
                      编辑信息
                    </a-doption>
                    <a-doption 
                      v-if="!version.isAutoSave" 
                      @click="handleDeleteVersion(version)"
                      class="text-red-500"
                    >
                      <span v-html="icon['delete'].body" class="mr-2"></span>
                      删除版本
                    </a-doption>
                    <a-doption @click="handleCompareVersion(version)">
                      <span v-html="icon['compare'].body" class="mr-2"></span>
                      与当前对比
                    </a-doption>
                  </template>
                </a-dropdown>
              </div>

              <!-- 版本详细信息 -->
              <div class="text-xs text-gray-500 space-y-1">
                <div>{{ formatTime(version.createdAt) }}</div>
                <div>作者: {{ version.author }}</div>
                <div>大小: {{ formatSize(version.size) }}</div>
                <div v-if="version.description" class="text-gray-600">
                  {{ version.description }}
                </div>
              </div>

              <!-- 版本操作按钮 -->
              <div class="flex items-center justify-between mt-2">
                <a-button 
                  size="small" 
                  @click="handlePreviewVersion(version)"
                  :loading="previewLoading && selectedVersion?.id === version.id"
                >
                  预览
                </a-button>
                
                <div class="flex space-x-2">
                  <a-button 
                    size="small" 
                    type="primary"
                    @click="handleRestoreVersion(version)"
                    :loading="restoreLoading && selectedVersion?.id === version.id"
                  >
                    恢复
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-spin>

        <!-- 加载更多 -->
        <div v-if="hasMore" class="text-center mt-4">
          <a-button @click="loadMoreVersions" :loading="loadingMore">
            加载更多
          </a-button>
        </div>
      </div>
    </a-drawer>

    <!-- 创建版本弹窗 -->
    <a-modal
      v-model:visible="createVersionVisible"
      title="保存版本"
      @ok="handleCreateVersionConfirm"
      @cancel="handleCreateVersionCancel"
      :ok-loading="createLoading"
    >
      <a-form :model="createVersionForm" layout="vertical">
        <a-form-item label="版本标题" required>
          <a-input 
            v-model="createVersionForm.title" 
            placeholder="请输入版本标题"
          />
        </a-form-item>
        <a-form-item label="版本描述">
          <a-textarea 
            v-model="createVersionForm.description" 
            placeholder="请输入版本描述（可选）"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑版本信息弹窗 -->
    <a-modal
      v-model:visible="editVersionVisible"
      title="编辑版本信息"
      @ok="handleEditVersionConfirm"
      @cancel="handleEditVersionCancel"
      :ok-loading="editLoading"
    >
      <a-form :model="editVersionForm" layout="vertical">
        <a-form-item label="版本标题" required>
          <a-input 
            v-model="editVersionForm.title" 
            placeholder="请输入版本标题"
          />
        </a-form-item>
        <a-form-item label="版本描述">
          <a-textarea 
            v-model="editVersionForm.description" 
            placeholder="请输入版本描述（可选）"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 版本预览弹窗 -->
    <a-modal
      v-model:visible="previewVisible"
      :title="`预览版本: ${selectedVersion?.title}`"
      width="80%"
      :footer="null"
    >
      <div class="version-preview max-h-96 overflow-y-auto">
        <div v-if="previewContent" v-html="previewContent" class="prose max-w-none"></div>
        <a-spin v-else :loading="previewLoading" class="w-full h-32 flex items-center justify-center" />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { versionApi } from '@/api/version'
import icon from '@/utils/icon'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  documentId: {
    type: String,
    default: ''
  },
  currentContent: {
    type: Object,
    default: null
  },
  onRestore: {
    type: Function,
    default: () => {}
  }
})

// Emits
const emit = defineEmits(['update:visible', 'restore'])

// 响应式数据
const versions = ref([])
const selectedVersion = ref(null)
const loading = ref(false)
const loadingMore = ref(false)
const createLoading = ref(false)
const editLoading = ref(false)
const restoreLoading = ref(false)
const previewLoading = ref(false)

// 分页信息
const currentPage = ref(1)
const totalVersions = ref(0)
const pageSize = ref(20)

// 弹窗状态
const createVersionVisible = ref(false)
const editVersionVisible = ref(false)
const previewVisible = ref(false)

// 表单数据
const createVersionForm = ref({
  title: '',
  description: ''
})

const editVersionForm = ref({
  title: '',
  description: ''
})

// 预览内容
const previewContent = ref('')

// 计算属性
const hasMore = computed(() => {
  return versions.value.length < totalVersions.value
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadVersions()
  }
})

// 方法
const handleClose = () => {
  emit('update:visible', false)
}

const loadVersions = async (page = 1) => {
  if (page === 1) {
    loading.value = true
    versions.value = []
  } else {
    loadingMore.value = true
  }

  try {
    const response = await versionApi.getVersions(props.documentId, page, pageSize.value)
    if (response.code === 200) {
      if (page === 1) {
        versions.value = response.data.versions
      } else {
        versions.value.push(...response.data.versions)
      }
      totalVersions.value = response.data.total
      currentPage.value = page
    }
  } catch (error) {
    console.error('加载版本列表失败:', error)
    Message.error('加载版本列表失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

const loadMoreVersions = () => {
  loadVersions(currentPage.value + 1)
}

// 创建版本
const handleCreateVersion = () => {
  createVersionForm.value = {
    title: `版本 ${new Date().toLocaleString()}`,
    description: ''
  }
  createVersionVisible.value = true
}

const handleCreateVersionConfirm = async () => {
  if (!createVersionForm.value.title.trim()) {
    Message.warning('请输入版本标题')
    return
  }

  createLoading.value = true
  try {
    const response = await versionApi.createVersion(props.documentId, {
      content: props.currentContent,
      title: createVersionForm.value.title.trim(),
      description: createVersionForm.value.description.trim(),
      isAutoSave: false,
      author: localStorage.getItem('username') || 'Anonymous'
    })

    if (response.code === 200) {
      Message.success('版本保存成功')
      createVersionVisible.value = false
      loadVersions() // 重新加载版本列表
    } else {
      Message.error(response.message || '版本保存失败')
    }
  } catch (error) {
    console.error('创建版本失败:', error)
    Message.error('版本保存失败')
  } finally {
    createLoading.value = false
  }
}

const handleCreateVersionCancel = () => {
  createVersionVisible.value = false
}

// 编辑版本信息
const handleEditVersion = (version) => {
  selectedVersion.value = version
  editVersionForm.value = {
    title: version.title,
    description: version.description
  }
  editVersionVisible.value = true
}

const handleEditVersionConfirm = async () => {
  if (!selectedVersion.value) return

  editLoading.value = true
  try {
    const response = await versionApi.updateVersion(
      props.documentId,
      selectedVersion.value.id,
      {
        title: editVersionForm.value.title.trim(),
        description: editVersionForm.value.description.trim()
      }
    )

    if (response.code === 200) {
      Message.success('版本信息更新成功')
      editVersionVisible.value = false
      loadVersions() // 重新加载版本列表
    } else {
      Message.error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('更新版本信息失败:', error)
    Message.error('更新失败')
  } finally {
    editLoading.value = false
  }
}

const handleEditVersionCancel = () => {
  editVersionVisible.value = false
  selectedVersion.value = null
}

// 删除版本
const handleDeleteVersion = (version) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除版本"${version.title}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        const response = await versionApi.deleteVersion(props.documentId, version.id)
        if (response.code === 200) {
          Message.success('版本删除成功')
          loadVersions() // 重新加载版本列表
        } else {
          Message.error(response.message || '删除失败')
        }
      } catch (error) {
        console.error('删除版本失败:', error)
        Message.error('删除失败')
      }
    }
  })
}

// 恢复版本
const handleRestoreVersion = (version) => {
  Modal.confirm({
    title: '确认恢复',
    content: `确定要恢复到版本"${version.title}"吗？当前未保存的内容将会丢失。`,
    onOk: async () => {
      restoreLoading.value = true
      selectedVersion.value = version
      
      try {
        const response = await versionApi.getVersion(props.documentId, version.id)
        if (response.code === 200) {
          emit('restore', response.data.content)
          Message.success('版本恢复成功')
          handleClose()
        } else {
          Message.error(response.message || '恢复失败')
        }
      } catch (error) {
        console.error('恢复版本失败:', error)
        Message.error('恢复失败')
      } finally {
        restoreLoading.value = false
        selectedVersion.value = null
      }
    }
  })
}

// 预览版本
const handlePreviewVersion = async (version) => {
  selectedVersion.value = version
  previewLoading.value = true
  previewVisible.value = true
  previewContent.value = ''

  try {
    const response = await versionApi.getVersion(props.documentId, version.id)
    if (response.code === 200) {
      // 这里需要将版本内容转换为HTML进行预览
      // 假设content是TipTap的JSON格式，需要转换为HTML
      previewContent.value = JSON.stringify(response.data.content, null, 2)
    } else {
      Message.error(response.message || '预览失败')
    }
  } catch (error) {
    console.error('预览版本失败:', error)
    Message.error('预览失败')
  } finally {
    previewLoading.value = false
  }
}

// 版本对比
const handleCompareVersion = (version) => {
  // TODO: 实现版本对比功能
  Message.info('版本对比功能开发中...')
}

// 工具函数
const formatTime = (timeString) => {
  return new Date(timeString).toLocaleString()
}

const formatSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.version-manager {
  /* 组件样式 */
}

.version-item {
  transition: all 0.2s ease;
}

.version-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-state {
  text-align: center;
  padding: 2rem;
}

.version-preview {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
}

.prose {
  line-height: 1.6;
}
</style>