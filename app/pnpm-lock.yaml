lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@fingerprintjs/fingerprintjs':
        specifier: ^4.6.2
        version: 4.6.2
      '@floating-ui/dom':
        specifier: ^1.6.12
        version: 1.6.12
      '@iconify/vue':
        specifier: ^4.2.0
        version: 4.2.0(vue@3.5.13(typescript@5.7.2))
      '@tiptap/core':
        specifier: ^2.26.1
        version: 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/extension-bold':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-bubble-menu':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-bullet-list':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-character-count':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-code':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-code-block':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-collaboration':
        specifier: ^2.22.3
        version: 2.22.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)(y-prosemirror@1.3.6(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0)(y-protocols@1.0.6(yjs@13.6.27))(yjs@13.6.27))
      '@tiptap/extension-collaboration-cursor':
        specifier: ^2.22.3
        version: 2.22.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(y-prosemirror@1.3.6(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0)(y-protocols@1.0.6(yjs@13.6.27))(yjs@13.6.27))
      '@tiptap/extension-document':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-dropcursor':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-gapcursor':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-image':
        specifier: ^2.22.3
        version: 2.22.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-italic':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-link':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-list-item':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-mention':
        specifier: ^3.0.7
        version: 3.0.7(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)(@tiptap/suggestion@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3))
      '@tiptap/extension-ordered-list':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-placeholder':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-strike':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-subscript':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-superscript':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-table':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-table-cell':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-table-header':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-table-row':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-task-item':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-task-list':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-text':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-typography':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/extension-underline':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))
      '@tiptap/pm':
        specifier: ^2.9.1
        version: 2.10.3
      '@tiptap/suggestion':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/vue-3':
        specifier: ^3.0.7
        version: 3.0.7(@floating-ui/dom@1.6.12)(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)(vue@3.5.13(typescript@5.7.2))
      '@vueuse/core':
        specifier: ^11.2.0
        version: 11.3.0(vue@3.5.13(typescript@5.7.2))
      axios:
        specifier: ^1.10.0
        version: 1.10.0
      es-drager:
        specifier: ^1.3.0
        version: 1.3.0(vue@3.5.13(typescript@5.7.2))
      i18next:
        specifier: ^23.16.5
        version: 23.16.8
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      pinia:
        specifier: ^2.2.4
        version: 2.3.0(typescript@5.7.2)(vue@3.5.13(typescript@5.7.2))
      prosemirror-state:
        specifier: ^1.4.3
        version: 1.4.3
      qrcode.vue:
        specifier: ^3.6.0
        version: 3.6.0(vue@3.5.13(typescript@5.7.2))
      shiki:
        specifier: ^1.24.0
        version: 1.24.0
      tippy.js:
        specifier: ^6.3.7
        version: 6.3.7
      uuid:
        specifier: ^11.0.2
        version: 11.0.3
      vue:
        specifier: ^3.5.12
        version: 3.5.13(typescript@5.7.2)
      vue-i18n:
        specifier: ^10.0.4
        version: 10.0.5(vue@3.5.13(typescript@5.7.2))
      vue-router:
        specifier: ^4.4.5
        version: 4.5.0(vue@3.5.13(typescript@5.7.2))
      y-websocket:
        specifier: ^3.0.0
        version: 3.0.0(yjs@13.6.27)
      yjs:
        specifier: ^13.6.27
        version: 13.6.27
    devDependencies:
      '@arco-design/web-vue':
        specifier: ^2.56.3
        version: 2.56.3(vue@3.5.13(typescript@5.7.2))
      '@eslint/js':
        specifier: ^9.13.0
        version: 9.16.0
      '@iconify-json/lucide':
        specifier: ^1.2.14
        version: 1.2.17
      '@iconify-json/tabler':
        specifier: ^1.2.10
        version: 1.2.10
      '@unocss/transformer-directives':
        specifier: ^0.63.6
        version: 0.63.6
      '@unocss/transformer-variant-group':
        specifier: ^0.63.6
        version: 0.63.6
      '@vitejs/plugin-vue':
        specifier: ^5.1.4
        version: 5.2.1(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))
      '@vitejs/plugin-vue-jsx':
        specifier: ^4.0.1
        version: 4.1.1(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))
      '@vue/eslint-config-prettier':
        specifier: ^10.0.0
        version: 10.1.0(@types/eslint@9.6.1)(eslint@9.16.0(jiti@2.4.1))(prettier@3.4.2)
      eslint:
        specifier: ^9.13.0
        version: 9.16.0(jiti@2.4.1)
      eslint-plugin-vue:
        specifier: ^9.29.0
        version: 9.32.0(eslint@9.16.0(jiti@2.4.1))
      prettier:
        specifier: ^3.3.3
        version: 3.4.2
      sass:
        specifier: ^1.80.6
        version: 1.82.0
      unocss:
        specifier: ^0.63.6
        version: 0.63.6(postcss@8.4.49)(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))
      unplugin-auto-import:
        specifier: ^0.18.3
        version: 0.18.6(@nuxt/kit@3.14.1592(rollup@4.28.0))(@vueuse/core@11.3.0(vue@3.5.13(typescript@5.7.2)))(rollup@4.28.0)
      unplugin-icons:
        specifier: ^0.20.0
        version: 0.20.2(@vue/compiler-sfc@3.5.13)
      unplugin-vue-components:
        specifier: ^0.27.4
        version: 0.27.5(@babel/parser@7.26.3)(@nuxt/kit@3.14.1592(rollup@4.28.0))(rollup@4.28.0)(vue@3.5.13(typescript@5.7.2))
      vite:
        specifier: ^5.4.10
        version: 5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0)
      vite-plugin-vue-devtools:
        specifier: ^7.5.3
        version: 7.6.7(@nuxt/kit@3.14.1592(rollup@4.28.0))(rollup@4.28.0)(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))

  docs:
    dependencies:
      '@vitepress-demo-preview/component':
        specifier: ^2.3.2
        version: 2.3.2(vitepress@1.0.0-rc.44(@algolia/client-search@5.15.0)(@types/node@22.10.1)(postcss@8.4.49)(sass@1.82.0)(search-insights@2.17.3)(terser@5.37.0)(typescript@5.7.2))(vue@3.5.13(typescript@5.7.2))
      '@vitepress-demo-preview/plugin':
        specifier: ^1.2.3
        version: 1.2.3(markdown-it-container@3.0.0)(vitepress@1.0.0-rc.44(@algolia/client-search@5.15.0)(@types/node@22.10.1)(postcss@8.4.49)(sass@1.82.0)(search-insights@2.17.3)(terser@5.37.0)(typescript@5.7.2))(vue@3.5.13(typescript@5.7.2))
      '@vueuse/core':
        specifier: ^10.11.1
        version: 10.11.1(vue@3.5.13(typescript@5.7.2))
      '@vueuse/motion':
        specifier: ^2.2.6
        version: 2.2.6(rollup@4.28.0)(vue@3.5.13(typescript@5.7.2))
    devDependencies:
      '@iconify-json/lucide':
        specifier: ^1.2.14
        version: 1.2.17
      '@iconify-json/tabler':
        specifier: ^1.2.10
        version: 1.2.10
      '@unocss/transformer-directives':
        specifier: ^0.63.6
        version: 0.63.6
      '@unocss/transformer-variant-group':
        specifier: ^0.63.6
        version: 0.63.6
      unocss:
        specifier: ^0.63.6
        version: 0.63.6(postcss@8.4.49)(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))
      unplugin-icons:
        specifier: ^0.20.0
        version: 0.20.2(@vue/compiler-sfc@3.5.13)
      unplugin-vue-components:
        specifier: ^0.27.4
        version: 0.27.5(@babel/parser@7.26.3)(@nuxt/kit@3.14.1592(rollup@4.28.0))(rollup@4.28.0)(vue@3.5.13(typescript@5.7.2))
      vitepress:
        specifier: 1.0.0-rc.44
        version: 1.0.0-rc.44(@algolia/client-search@5.15.0)(@types/node@22.10.1)(postcss@8.4.49)(sass@1.82.0)(search-insights@2.17.3)(terser@5.37.0)(typescript@5.7.2)
      vue:
        specifier: ^3.5.12
        version: 3.5.13(typescript@5.7.2)

  packages/core:
    dependencies:
      '@floating-ui/dom':
        specifier: ^1.6.12
        version: 1.6.12
      '@tiptap/core':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/pm@2.10.3)
      '@tiptap/extension-bold':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-bubble-menu':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-bullet-list':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-character-count':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-code':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-code-block':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-document':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-dropcursor':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-gapcursor':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-italic':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-link':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-list-item':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-ordered-list':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-placeholder':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-strike':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-subscript':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-superscript':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-table':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-table-cell':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-table-header':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-table-row':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-task-item':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-task-list':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-text':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-typography':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-underline':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/pm':
        specifier: ^2.9.1
        version: 2.10.3
      '@tiptap/suggestion':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      i18next:
        specifier: ^23.16.5
        version: 23.16.8
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      shiki:
        specifier: ^1.24.0
        version: 1.24.0
      uuid:
        specifier: ^11.0.2
        version: 11.0.3

  packages/vue3:
    dependencies:
      '@iconify/vue':
        specifier: ^4.2.0
        version: 4.2.0(vue@3.5.13(typescript@5.7.2))
      '@isle-editor/core':
        specifier: workspace:*
        version: link:../core
      tippy.js:
        specifier: ^6.3.7
        version: 6.3.7
      uuid:
        specifier: ^11.0.2
        version: 11.0.3
      vue:
        specifier: ^3.0.0
        version: 3.5.13(typescript@5.7.2)

  playground:
    dependencies:
      '@floating-ui/dom':
        specifier: ^1.6.12
        version: 1.6.12
      '@iconify/vue':
        specifier: ^4.2.0
        version: 4.2.0(vue@3.5.13(typescript@5.7.2))
      '@tiptap/core':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/pm@2.10.3)
      '@tiptap/extension-bold':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-bubble-menu':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-bullet-list':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-character-count':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-code':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-code-block':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-collaboration':
        specifier: ^2.22.3
        version: 2.22.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)(y-prosemirror@1.3.6(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0)(y-protocols@1.0.6(yjs@13.6.27))(yjs@13.6.27))
      '@tiptap/extension-collaboration-cursor':
        specifier: ^2.22.3
        version: 2.22.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(y-prosemirror@1.3.6(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0)(y-protocols@1.0.6(yjs@13.6.27))(yjs@13.6.27))
      '@tiptap/extension-document':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-dropcursor':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-gapcursor':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-italic':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-link':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-list-item':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-ordered-list':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-placeholder':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-strike':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-subscript':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-superscript':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-table':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-table-cell':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-table-header':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-table-row':
        specifier: ^2.10.3
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-task-item':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-task-list':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-text':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-typography':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/extension-underline':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))
      '@tiptap/pm':
        specifier: ^2.9.1
        version: 2.10.3
      '@tiptap/suggestion':
        specifier: ^2.9.1
        version: 2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@vueuse/core':
        specifier: ^11.2.0
        version: 11.3.0(vue@3.5.13(typescript@5.7.2))
      i18next:
        specifier: ^23.16.5
        version: 23.16.8
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      pinia:
        specifier: ^2.2.4
        version: 2.3.0(typescript@5.7.2)(vue@3.5.13(typescript@5.7.2))
      shiki:
        specifier: ^1.24.0
        version: 1.24.0
      tippy.js:
        specifier: ^6.3.7
        version: 6.3.7
      uuid:
        specifier: ^11.0.2
        version: 11.0.3
      vue:
        specifier: ^3.5.12
        version: 3.5.13(typescript@5.7.2)
      vue-i18n:
        specifier: ^10.0.4
        version: 10.0.5(vue@3.5.13(typescript@5.7.2))
      vue-router:
        specifier: ^4.4.5
        version: 4.5.0(vue@3.5.13(typescript@5.7.2))
      y-websocket:
        specifier: ^3.0.0
        version: 3.0.0(yjs@13.6.27)
      yjs:
        specifier: ^13.6.27
        version: 13.6.27
    devDependencies:
      '@arco-design/web-vue':
        specifier: ^2.56.3
        version: 2.56.3(vue@3.5.13(typescript@5.7.2))
      '@eslint/js':
        specifier: ^9.13.0
        version: 9.16.0
      '@iconify-json/lucide':
        specifier: ^1.2.14
        version: 1.2.17
      '@iconify-json/tabler':
        specifier: ^1.2.10
        version: 1.2.10
      '@unocss/transformer-directives':
        specifier: ^0.63.6
        version: 0.63.6
      '@unocss/transformer-variant-group':
        specifier: ^0.63.6
        version: 0.63.6
      '@vitejs/plugin-vue':
        specifier: ^5.1.4
        version: 5.2.1(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))
      '@vitejs/plugin-vue-jsx':
        specifier: ^4.0.1
        version: 4.1.1(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))
      '@vue/eslint-config-prettier':
        specifier: ^10.0.0
        version: 10.1.0(@types/eslint@9.6.1)(eslint@9.16.0(jiti@2.4.1))(prettier@3.4.2)
      eslint:
        specifier: ^9.13.0
        version: 9.16.0(jiti@2.4.1)
      eslint-plugin-vue:
        specifier: ^9.29.0
        version: 9.32.0(eslint@9.16.0(jiti@2.4.1))
      prettier:
        specifier: ^3.3.3
        version: 3.4.2
      sass:
        specifier: ^1.80.6
        version: 1.82.0
      unocss:
        specifier: ^0.63.6
        version: 0.63.6(postcss@8.4.49)(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))
      unplugin-auto-import:
        specifier: ^0.18.3
        version: 0.18.6(@nuxt/kit@3.14.1592(rollup@4.28.0))(@vueuse/core@11.3.0(vue@3.5.13(typescript@5.7.2)))(rollup@4.28.0)
      unplugin-icons:
        specifier: ^0.20.0
        version: 0.20.2(@vue/compiler-sfc@3.5.13)
      unplugin-vue-components:
        specifier: ^0.27.4
        version: 0.27.5(@babel/parser@7.26.3)(@nuxt/kit@3.14.1592(rollup@4.28.0))(rollup@4.28.0)(vue@3.5.13(typescript@5.7.2))
      vite:
        specifier: ^5.4.10
        version: 5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)
      vite-plugin-vue-devtools:
        specifier: ^7.5.3
        version: 7.6.7(@nuxt/kit@3.14.1592(rollup@4.28.0))(rollup@4.28.0)(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))

  shared: {}

packages:

  '@algolia/autocomplete-core@1.17.7':
    resolution: {integrity: sha512-BjiPOW6ks90UKl7TwMv7oNQMnzU+t/wk9mgIDi6b1tXpUek7MW0lbNOUHpvam9pe3lVCf4xPFT+lK7s+e+fs7Q==}

  '@algolia/autocomplete-plugin-algolia-insights@1.17.7':
    resolution: {integrity: sha512-Jca5Ude6yUOuyzjnz57og7Et3aXjbwCSDf/8onLHSQgw1qW3ALl9mrMWaXb5FmPVkV3EtkD2F/+NkT6VHyPu9A==}
    peerDependencies:
      search-insights: '>= 1 < 3'

  '@algolia/autocomplete-preset-algolia@1.17.7':
    resolution: {integrity: sha512-ggOQ950+nwbWROq2MOCIL71RE0DdQZsceqrg32UqnhDz8FlO9rL8ONHNsI2R1MH0tkgVIDKI/D0sMiUchsFdWA==}
    peerDependencies:
      '@algolia/client-search': '>= 4.9.1 < 6'
      algoliasearch: '>= 4.9.1 < 6'

  '@algolia/autocomplete-shared@1.17.7':
    resolution: {integrity: sha512-o/1Vurr42U/qskRSuhBH+VKxMvkkUVTLU6WZQr+L5lGZZLYWyhdzWjW0iGXY7EkwRTjBqvN2EsR81yCTGV/kmg==}
    peerDependencies:
      '@algolia/client-search': '>= 4.9.1 < 6'
      algoliasearch: '>= 4.9.1 < 6'

  '@algolia/client-abtesting@5.15.0':
    resolution: {integrity: sha512-FaEM40iuiv1mAipYyiptP4EyxkJ8qHfowCpEeusdHUC4C7spATJYArD2rX3AxkVeREkDIgYEOuXcwKUbDCr7Nw==}
    engines: {node: '>= 14.0.0'}

  '@algolia/client-analytics@5.15.0':
    resolution: {integrity: sha512-lho0gTFsQDIdCwyUKTtMuf9nCLwq9jOGlLGIeQGKDxXF7HbiAysFIu5QW/iQr1LzMgDyM9NH7K98KY+BiIFriQ==}
    engines: {node: '>= 14.0.0'}

  '@algolia/client-common@5.15.0':
    resolution: {integrity: sha512-IofrVh213VLsDkPoSKMeM9Dshrv28jhDlBDLRcVJQvlL8pzue7PEB1EZ4UoJFYS3NSn7JOcJ/V+olRQzXlJj1w==}
    engines: {node: '>= 14.0.0'}

  '@algolia/client-insights@5.15.0':
    resolution: {integrity: sha512-bDDEQGfFidDi0UQUCbxXOCdphbVAgbVmxvaV75cypBTQkJ+ABx/Npw7LkFGw1FsoVrttlrrQbwjvUB6mLVKs/w==}
    engines: {node: '>= 14.0.0'}

  '@algolia/client-personalization@5.15.0':
    resolution: {integrity: sha512-LfaZqLUWxdYFq44QrasCDED5bSYOswpQjSiIL7Q5fYlefAAUO95PzBPKCfUhSwhb4rKxigHfDkd81AvEicIEoA==}
    engines: {node: '>= 14.0.0'}

  '@algolia/client-query-suggestions@5.15.0':
    resolution: {integrity: sha512-wu8GVluiZ5+il8WIRsGKu8VxMK9dAlr225h878GGtpTL6VBvwyJvAyLdZsfFIpY0iN++jiNb31q2C1PlPL+n/A==}
    engines: {node: '>= 14.0.0'}

  '@algolia/client-search@5.15.0':
    resolution: {integrity: sha512-Z32gEMrRRpEta5UqVQA612sLdoqY3AovvUPClDfMxYrbdDAebmGDVPtSogUba1FZ4pP5dx20D3OV3reogLKsRA==}
    engines: {node: '>= 14.0.0'}

  '@algolia/ingestion@1.15.0':
    resolution: {integrity: sha512-MkqkAxBQxtQ5if/EX2IPqFA7LothghVyvPoRNA/meS2AW2qkHwcxjuiBxv4H6mnAVEPfJlhu9rkdVz9LgCBgJg==}
    engines: {node: '>= 14.0.0'}

  '@algolia/monitoring@1.15.0':
    resolution: {integrity: sha512-QPrFnnGLMMdRa8t/4bs7XilPYnoUXDY8PMQJ1sf9ZFwhUysYYhQNX34/enoO0LBjpoOY6rLpha39YQEFbzgKyQ==}
    engines: {node: '>= 14.0.0'}

  '@algolia/recommend@5.15.0':
    resolution: {integrity: sha512-5eupMwSqMLDObgSMF0XG958zR6GJP3f7jHDQ3/WlzCM9/YIJiWIUoJFGsko9GYsA5xbLDHE/PhWtq4chcCdaGQ==}
    engines: {node: '>= 14.0.0'}

  '@algolia/requester-browser-xhr@5.15.0':
    resolution: {integrity: sha512-Po/GNib6QKruC3XE+WKP1HwVSfCDaZcXu48kD+gwmtDlqHWKc7Bq9lrS0sNZ456rfCKhXksOmMfUs4wRM/Y96w==}
    engines: {node: '>= 14.0.0'}

  '@algolia/requester-fetch@5.15.0':
    resolution: {integrity: sha512-rOZ+c0P7ajmccAvpeeNrUmEKoliYFL8aOR5qGW5pFq3oj3Iept7Y5mEtEsOBYsRt6qLnaXn4zUKf+N8nvJpcIw==}
    engines: {node: '>= 14.0.0'}

  '@algolia/requester-node-http@5.15.0':
    resolution: {integrity: sha512-b1jTpbFf9LnQHEJP5ddDJKE2sAlhYd7EVSOWgzo/27n/SfCoHfqD0VWntnWYD83PnOKvfe8auZ2+xCb0TXotrQ==}
    engines: {node: '>= 14.0.0'}

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@antfu/install-pkg@0.4.1':
    resolution: {integrity: sha512-T7yB5QNG29afhWVkVq7XeIMBa5U/vs9mX69YqayXypPRmYzUmzwnYltplHmPtZ4HPCn+sQKeXW8I47wCbuBOjw==}

  '@antfu/install-pkg@0.5.0':
    resolution: {integrity: sha512-dKnk2xlAyC7rvTkpkHmu+Qy/2Zc3Vm/l8PtNyIOGDBtXPY3kThfU4ORNEp3V7SXw5XSOb+tOJaUYpfquPzL/Tg==}

  '@antfu/utils@0.7.10':
    resolution: {integrity: sha512-+562v9k4aI80m1+VuMHehNJWLOFjBnXn3tdOitzD0il5b7smkSBal4+a3oKiQTbrwMmN/TBUMDvbdoWDehgOww==}

  '@arco-design/color@0.4.0':
    resolution: {integrity: sha512-s7p9MSwJgHeL8DwcATaXvWT3m2SigKpxx4JA1BGPHL4gfvaQsmQfrLBDpjOJFJuJ2jG2dMt3R3P8Pm9E65q18g==}

  '@arco-design/web-vue@2.56.3':
    resolution: {integrity: sha512-D2CPIXRBUPcg37TFsfWROZddCWFZnIwqGpsOhOn2BhmH89UFqtBGpTxyuMdYJEwKNXunp3dVL6V69ZMmJBRPOg==}
    peerDependencies:
      vue: ^3.1.0

  '@babel/code-frame@7.26.2':
    resolution: {integrity: sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.26.3':
    resolution: {integrity: sha512-nHIxvKPniQXpmQLb0vhY3VaFb3S0YrTAwpOWJZh1wn3oJPjJk9Asva204PsBdmAE8vpzfHudT8DB0scYvy9q0g==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.26.0':
    resolution: {integrity: sha512-i1SLeK+DzNnQ3LL/CswPCa/E5u4lh1k6IAEphON8F+cXt0t9euTshDru0q7/IqMa1PMPz5RnHuHscF8/ZJsStg==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.26.3':
    resolution: {integrity: sha512-6FF/urZvD0sTeO7k6/B15pMLC4CHUv1426lzr3N01aHJTl046uCAh9LXW/fzeXXjPNCJ6iABW5XaWOsIZB93aQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.25.9':
    resolution: {integrity: sha512-gv7320KBUFJz1RnylIg5WWYPRXKZ884AGkYpgpWW02TH66Dl+HaC1t1CKd0z3R4b6hdYEcmrNZHUmfCP+1u3/g==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.25.9':
    resolution: {integrity: sha512-j9Db8Suy6yV/VHa4qzrj9yZfZxhLWQdVnRlXxmKLYlhWUVB1sB2G5sxuWYXk/whHD9iW76PmNzxZ4UCnTQTVEQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.25.9':
    resolution: {integrity: sha512-UTZQMvt0d/rSz6KI+qdu7GQze5TIajwTS++GUozlw8VBJDEOAqSXwm1WvmYEZwqdqSGQshRocPDqrt4HBZB3fQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-member-expression-to-functions@7.25.9':
    resolution: {integrity: sha512-wbfdZ9w5vk0C0oyHqAJbc62+vet5prjj01jjJ8sKn3j9h3MQQlflEdXYvuqRWjHnM12coDEqiC1IRCi0U/EKwQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.25.9':
    resolution: {integrity: sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.26.0':
    resolution: {integrity: sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.25.9':
    resolution: {integrity: sha512-FIpuNaz5ow8VyrYcnXQTDRGvV6tTjkNtCK/RYNDXGSLlUD6cBuQTSw43CShGxjvfBTfcUA/r6UhUCbtYqkhcuQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.25.9':
    resolution: {integrity: sha512-kSMlyUVdWe25rEsRGviIgOWnoT/nfABVWlqt9N19/dIPWViAOW2s9wznP5tURbs/IDuNk4gPy3YdYRgH3uxhBw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-replace-supers@7.25.9':
    resolution: {integrity: sha512-IiDqTOTBQy0sWyeXyGSC5TBJpGFXBkRynjBeXsvbhQFKj2viwJC76Epz35YLU1fpe/Am6Vppb7W7zM4fPQzLsQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    resolution: {integrity: sha512-K4Du3BFa3gvyhzgPcntrkDgZzQaq6uozzcpGbOO1OEJaI+EJdqWIMTLgFgQf6lrfiDFo5FU+BxKepI9RmZqahA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.25.9':
    resolution: {integrity: sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.25.9':
    resolution: {integrity: sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.25.9':
    resolution: {integrity: sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.26.0':
    resolution: {integrity: sha512-tbhNuIxNcVb21pInl3ZSjksLCvgdZy9KwJ8brv993QtIVKJBBkYXz4q4ZbAv31GdnC+R90np23L5FbEBlthAEw==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.26.3':
    resolution: {integrity: sha512-WJ/CvmY8Mea8iDXo6a7RK2wbmJITT5fN3BEkRuFlxVyNx8jOKIIhmC4fSkTcPcf8JyavbBwIe6OpiCOBXt/IcA==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-proposal-decorators@7.25.9':
    resolution: {integrity: sha512-smkNLL/O1ezy9Nhy4CNosc4Va+1wo5w4gzSZeLe6y6dM4mmHfYOCPolXQPHQxonZCF+ZyebxN9vqOolkYrSn5g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-decorators@7.25.9':
    resolution: {integrity: sha512-ryzI0McXUPJnRCvMo4lumIKZUzhYUO/ScI+Mz4YVaTLt04DHNSjEUjKVvbzQjZFLuod/cYEc07mJWhzl6v4DPg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-attributes@7.26.0':
    resolution: {integrity: sha512-e2dttdsJ1ZTpi3B9UYGLw41hifAubg19AtCu/2I/F1QNVclOBr1dYpTdmdyZ84Xiz43BS/tCUkMAZNLv12Pi+A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-meta@7.10.4':
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-jsx@7.25.9':
    resolution: {integrity: sha512-ld6oezHQMZsZfp6pWtbjaNDF2tiiCYYDqQszHt5VV437lewP9aSi2Of99CK0D0XB21k7FLgnLcmQKyKzynfeAA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-typescript@7.25.9':
    resolution: {integrity: sha512-hjMgRy5hb8uJJjUcdWunWVcoi9bGpJp8p5Ol1229PoN6aytsLwNMgmdftO23wnCLMfVmTwZDWMPNq/D1SY60JQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typescript@7.26.3':
    resolution: {integrity: sha512-6+5hpdr6mETwSKjmJUdYw0EIkATiQhnELWlE3kJFBwSg/BGIVwVaVbX+gOXBCdc7Ln1RXZxyWGecIXhUfnl7oA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime@7.26.0':
    resolution: {integrity: sha512-FDSOghenHTiToteC/QRlv2q3DhPZ/oOXTBoirfWNx1Cx3TMVcGWQtMMmQcSvb/JjpNeGzx8Pq/b4fKEJuWm1sw==}
    engines: {node: '>=6.9.0'}

  '@babel/standalone@7.26.4':
    resolution: {integrity: sha512-SF+g7S2mhTT1b7CHyfNjDkPU1corxg4LPYsyP0x5KuCl+EbtBQHRLqr9N3q7e7+x7NQ5LYxQf8mJ2PmzebLr0A==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.25.9':
    resolution: {integrity: sha512-9DGttpmPvIxBb/2uwpVo3dqJ+O6RooAFOS+lB+xDqoE2PVCE8nfoHMdZLpfCQRLwvohzXISPZcgxt80xLfsuwg==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.26.4':
    resolution: {integrity: sha512-fH+b7Y4p3yqvApJALCPJcwb0/XaOSgtK4pzV6WVjPR5GLFQBRI7pfoX2V2iM48NXvX07NUxxm1Vw98YjqTcU5w==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.26.3':
    resolution: {integrity: sha512-vN5p+1kl59GVKMvTHt55NzzmYVxprfJD+ql7U9NFIfKCBkYE55LYtS+WtPlaYOyzydrKI8Nezd+aZextrd+FMA==}
    engines: {node: '>=6.9.0'}

  '@docsearch/css@3.8.0':
    resolution: {integrity: sha512-pieeipSOW4sQ0+bE5UFC51AOZp9NGxg89wAlZ1BAQFaiRAGK1IKUaPQ0UGZeNctJXyqZ1UvBtOQh2HH+U5GtmA==}

  '@docsearch/js@3.8.0':
    resolution: {integrity: sha512-PVuV629f5UcYRtBWqK7ID6vNL5647+2ADJypwTjfeBIrJfwPuHtzLy39hMGMfFK+0xgRyhTR0FZ83EkdEraBlg==}

  '@docsearch/react@3.8.0':
    resolution: {integrity: sha512-WnFK720+iwTVt94CxY3u+FgX6exb3BfN5kE9xUY6uuAH/9W/UFboBZFLlrw/zxFRHoHZCOXRtOylsXF+6LHI+Q==}
    peerDependencies:
      '@types/react': '>= 16.8.0 < 19.0.0'
      react: '>= 16.8.0 < 19.0.0'
      react-dom: '>= 16.8.0 < 19.0.0'
      search-insights: '>= 1 < 3'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      react:
        optional: true
      react-dom:
        optional: true
      search-insights:
        optional: true

  '@esbuild/aix-ppc64@0.21.5':
    resolution: {integrity: sha512-1SDgH6ZSPTlggy1yI6+Dbkiz8xzpHJEVAlF/AM1tHPLsf5STom9rwtjE4hKAF20FfXXNTFqEYXyJNWh1GiZedQ==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/aix-ppc64@0.23.1':
    resolution: {integrity: sha512-6VhYk1diRqrhBAqpJEdjASR/+WVRtfjpqKuNw11cLiaWpAT/Uu+nokB+UJnevzy/P9C/ty6AOe0dwueMrGh/iQ==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.21.5':
    resolution: {integrity: sha512-c0uX9VAUBQ7dTDCjq+wdyGLowMdtR/GoC2U5IYk/7D1H1JYC0qseD7+11iMP2mRLN9RcCMRcjC4YMclCzGwS/A==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm64@0.23.1':
    resolution: {integrity: sha512-xw50ipykXcLstLeWH7WRdQuysJqejuAGPd30vd1i5zSyKK3WE+ijzHmLKxdiCMtH1pHz78rOg0BKSYOSB/2Khw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.21.5':
    resolution: {integrity: sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-arm@0.23.1':
    resolution: {integrity: sha512-uz6/tEy2IFm9RYOyvKl88zdzZfwEfKZmnX9Cj1BHjeSGNuGLuMD1kR8y5bteYmwqKm1tj8m4cb/aKEorr6fHWQ==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.21.5':
    resolution: {integrity: sha512-D7aPRUUNHRBwHxzxRvp856rjUHRFW1SdQATKXH2hqA0kAZb1hKmi02OpYRacl0TxIGz/ZmXWlbZgjwWYaCakTA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]

  '@esbuild/android-x64@0.23.1':
    resolution: {integrity: sha512-nlN9B69St9BwUoB+jkyU090bru8L0NA3yFvAd7k8dNsVH8bi9a8cUAUSEcEEgTp2z3dbEDGJGfP6VUnkQnlReg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.21.5':
    resolution: {integrity: sha512-DwqXqZyuk5AiWWf3UfLiRDJ5EDd49zg6O9wclZ7kUMv2WRFr4HKjXp/5t8JZ11QbQfUS6/cRCKGwYhtNAY88kQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-arm64@0.23.1':
    resolution: {integrity: sha512-YsS2e3Wtgnw7Wq53XXBLcV6JhRsEq8hkfg91ESVadIrzr9wO6jJDMZnCQbHm1Guc5t/CdDiFSSfWP58FNuvT3Q==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.21.5':
    resolution: {integrity: sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/darwin-x64@0.23.1':
    resolution: {integrity: sha512-aClqdgTDVPSEGgoCS8QDG37Gu8yc9lTHNAQlsztQ6ENetKEO//b8y31MMu2ZaPbn4kVsIABzVLXYLhCGekGDqw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.21.5':
    resolution: {integrity: sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-arm64@0.23.1':
    resolution: {integrity: sha512-h1k6yS8/pN/NHlMl5+v4XPfikhJulk4G+tKGFIOwURBSFzE8bixw1ebjluLOjfwtLqY0kewfjLSrO6tN2MgIhA==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.21.5':
    resolution: {integrity: sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.23.1':
    resolution: {integrity: sha512-lK1eJeyk1ZX8UklqFd/3A60UuZ/6UVfGT2LuGo3Wp4/z7eRTRYY+0xOu2kpClP+vMTi9wKOfXi2vjUpO1Ro76g==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.21.5':
    resolution: {integrity: sha512-ibKvmyYzKsBeX8d8I7MH/TMfWDXBF3db4qM6sy+7re0YXya+K1cem3on9XgdT2EQGMu4hQyZhan7TeQ8XkGp4Q==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm64@0.23.1':
    resolution: {integrity: sha512-/93bf2yxencYDnItMYV/v116zff6UyTjo4EtEQjUBeGiVpMmffDNUyD9UN2zV+V3LRV3/on4xdZ26NKzn6754g==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.21.5':
    resolution: {integrity: sha512-bPb5AHZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-arm@0.23.1':
    resolution: {integrity: sha512-CXXkzgn+dXAPs3WBwE+Kvnrf4WECwBdfjfeYHpMeVxWE0EceB6vhWGShs6wi0IYEqMSIzdOF1XjQ/Mkm5d7ZdQ==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.21.5':
    resolution: {integrity: sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-ia32@0.23.1':
    resolution: {integrity: sha512-VTN4EuOHwXEkXzX5nTvVY4s7E/Krz7COC8xkftbbKRYAl96vPiUssGkeMELQMOnLOJ8k3BY1+ZY52tttZnHcXQ==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.21.5':
    resolution: {integrity: sha512-uHf1BmMG8qEvzdrzAqg2SIG/02+4/DHB6a9Kbya0XDvwDEKCoC8ZRWI5JJvNdUjtciBGFQ5PuBlpEOXQj+JQSg==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-loong64@0.23.1':
    resolution: {integrity: sha512-Vx09LzEoBa5zDnieH8LSMRToj7ir/Jeq0Gu6qJ/1GcBq9GkfoEAoXvLiW1U9J1qE/Y/Oyaq33w5p2ZWrNNHNEw==}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.21.5':
    resolution: {integrity: sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-mips64el@0.23.1':
    resolution: {integrity: sha512-nrFzzMQ7W4WRLNUOU5dlWAqa6yVeI0P78WKGUo7lg2HShq/yx+UYkeNSE0SSfSure0SqgnsxPvmAUu/vu0E+3Q==}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.21.5':
    resolution: {integrity: sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-ppc64@0.23.1':
    resolution: {integrity: sha512-dKN8fgVqd0vUIjxuJI6P/9SSSe/mB9rvA98CSH2sJnlZ/OCZWO1DJvxj8jvKTfYUdGfcq2dDxoKaC6bHuTlgcw==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.21.5':
    resolution: {integrity: sha512-2HdXDMd9GMgTGrPWnJzP2ALSokE/0O5HhTUvWIbD3YdjME8JwvSCnNGBnTThKGEB91OZhzrJ4qIIxk/SBmyDDA==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-riscv64@0.23.1':
    resolution: {integrity: sha512-5AV4Pzp80fhHL83JM6LoA6pTQVWgB1HovMBsLQ9OZWLDqVY8MVobBXNSmAJi//Csh6tcY7e7Lny2Hg1tElMjIA==}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.21.5':
    resolution: {integrity: sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-s390x@0.23.1':
    resolution: {integrity: sha512-9ygs73tuFCe6f6m/Tb+9LtYxWR4c9yg7zjt2cYkjDbDpV/xVn+68cQxMXCjUpYwEkze2RcU/rMnfIXNRFmSoDw==}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.21.5':
    resolution: {integrity: sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]

  '@esbuild/linux-x64@0.23.1':
    resolution: {integrity: sha512-EV6+ovTsEXCPAp58g2dD68LxoP/wK5pRvgy0J/HxPGB009omFPv3Yet0HiaqvrIrgPTBuC6wCH1LTOY91EO5hQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-x64@0.21.5':
    resolution: {integrity: sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.23.1':
    resolution: {integrity: sha512-aevEkCNu7KlPRpYLjwmdcuNz6bDFiE7Z8XC4CPqExjTvrHugh28QzUXVOZtiYghciKUacNktqxdpymplil1beA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.23.1':
    resolution: {integrity: sha512-3x37szhLexNA4bXhLrCC/LImN/YtWis6WXr1VESlfVtVeoFJBRINPJ3f0a/6LV8zpikqoUg4hyXw0sFBt5Cr+Q==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.21.5':
    resolution: {integrity: sha512-HLNNw99xsvx12lFBUwoT8EVCsSvRNDVxNpjZ7bPn947b8gJPzeHWyNVhFsaerc0n3TsbOINvRP2byTZ5LKezow==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.23.1':
    resolution: {integrity: sha512-aY2gMmKmPhxfU+0EdnN+XNtGbjfQgwZj43k8G3fyrDM/UdZww6xrWxmDkuz2eCZchqVeABjV5BpildOrUbBTqA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.21.5':
    resolution: {integrity: sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/sunos-x64@0.23.1':
    resolution: {integrity: sha512-RBRT2gqEl0IKQABT4XTj78tpk9v7ehp+mazn2HbUeZl1YMdaGAQqhapjGTCe7uw7y0frDi4gS0uHzhvpFuI1sA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.21.5':
    resolution: {integrity: sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-arm64@0.23.1':
    resolution: {integrity: sha512-4O+gPR5rEBe2FpKOVyiJ7wNDPA8nGzDuJ6gN4okSA1gEOYZ67N8JPk58tkWtdtPeLz7lBnY6I5L3jdsr3S+A6A==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.21.5':
    resolution: {integrity: sha512-SWXFF1CL2RVNMaVs+BBClwtfZSvDgtL//G/smwAc5oVK/UPu2Gu9tIaRgFmYFFKrmg3SyAjSrElf0TiJ1v8fYA==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-ia32@0.23.1':
    resolution: {integrity: sha512-BcaL0Vn6QwCwre3Y717nVHZbAa4UBEigzFm6VdsVdT/MbZ38xoj1X9HPkZhbmaBGUD1W8vxAfffbDe8bA6AKnQ==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.21.5':
    resolution: {integrity: sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]

  '@esbuild/win32-x64@0.23.1':
    resolution: {integrity: sha512-BHpFFeslkWrXWyUPnbKm+xYYVYruCinGcftSBaa8zoF9hZO4BcSCFUvHVTtzpIY6YzUnYtuEhZ+C9iEXjxnasg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@eslint-community/eslint-utils@4.4.1':
    resolution: {integrity: sha512-s3O3waFUrMV8P/XaF/+ZTp1X9XBZW1a4B97ZnjQF2KYWaFD2A8KyFBsrsfSjEmjn3RGWAIuvlneuZm3CUK3jbA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/config-array@0.19.1':
    resolution: {integrity: sha512-fo6Mtm5mWyKjA/Chy1BYTdn5mGJoDNjC7C64ug20ADsRDGrA85bN3uK3MaKbeRkRuuIEAR5N33Jr1pbm411/PA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.9.1':
    resolution: {integrity: sha512-GuUdqkyyzQI5RMIWkHhvTWLCyLo1jNK3vzkSyaExH5kHPDHcuL2VOpHjmMY+y3+NC69qAKToBqldTBgYeLSr9Q==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/eslintrc@3.2.0':
    resolution: {integrity: sha512-grOjVNN8P3hjJn/eIETF1wwd12DdnwFDoyceUJLYYdkpbwq3nLi+4fqrTAONx7XDALqlL220wC/RHSC/QTI/0w==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/js@9.16.0':
    resolution: {integrity: sha512-tw2HxzQkrbeuvyj1tG2Yqq+0H9wGoI2IMk4EOsQeX+vmd75FtJAzf+gTA69WF+baUKRYQ3x2kbLE08js5OsTVg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/object-schema@2.1.5':
    resolution: {integrity: sha512-o0bhxnL89h5Bae5T318nFoFzGy+YE5i/gGkoPAgkmTVdRKTiv3p8JHevPiPaMwoloKfEiiaHlawCqaZMqRm+XQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/plugin-kit@0.2.4':
    resolution: {integrity: sha512-zSkKow6H5Kdm0ZUQUB2kV5JIXqoG0+uH5YADhaEHswm664N9Db8dXSi0nMJpacpMf+MyyglF1vnZohpEg5yUtg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@fingerprintjs/fingerprintjs@4.6.2':
    resolution: {integrity: sha512-g8mXuqcFKbgH2CZKwPfVtsUJDHyvcgIABQI7Y0tzWEFXpGxJaXuAuzlifT2oTakjDBLTK4Gaa9/5PERDhqUjtw==}

  '@floating-ui/core@1.6.8':
    resolution: {integrity: sha512-7XJ9cPU+yI2QeLS+FCSlqNFZJq8arvswefkZrYI1yQBbftw6FyrZOxYSh+9S7z7TpeWlRt9zJ5IhM1WIL334jA==}

  '@floating-ui/dom@1.6.12':
    resolution: {integrity: sha512-NP83c0HjokcGVEMeoStg317VD9W7eDlGK7457dMBANbKA6GJZdc7rjujdgqzTaz93jkGgc5P/jeWbaCHnMNc+w==}

  '@floating-ui/utils@0.2.8':
    resolution: {integrity: sha512-kym7SodPp8/wloecOpcmSnWJsK7M0E5Wg8UcFA+uO4B9s5d0ywXOEro/8HM9x0rW+TljRzul/14UYz3TleT3ig==}

  '@flypeng/tool@5.5.4':
    resolution: {integrity: sha512-lf8DY5DraZbomVZqwMBs9RWNoPO2K3Oo3ZFDyWDasZt6avCqFQVrFp3U61bibTpkQyFjzE5h3CfTN9h1bUB8EQ==}

  '@humanfs/core@0.19.1':
    resolution: {integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==}
    engines: {node: '>=18.18.0'}

  '@humanfs/node@0.16.6':
    resolution: {integrity: sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==}
    engines: {node: '>=18.18.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}

  '@humanwhocodes/retry@0.3.1':
    resolution: {integrity: sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==}
    engines: {node: '>=18.18'}

  '@humanwhocodes/retry@0.4.1':
    resolution: {integrity: sha512-c7hNEllBlenFTHBky65mhq8WD2kbN9Q6gk0bTk8lSBvc554jpXSkST1iePudpt7+A/AQvuHs9EMqjHDXMY1lrA==}
    engines: {node: '>=18.18'}

  '@iconify-json/lucide@1.2.17':
    resolution: {integrity: sha512-y+4P1DxD2h4d4fGYxikUdMf0o21DD0GIE/YIgixEBIXKbE90LTOFqmoxkGyPpaGk3vT2qE2w/28+sdmBMFsd5w==}

  '@iconify-json/tabler@1.2.10':
    resolution: {integrity: sha512-P58U/e0rZNYptzMjnExcFOtSt712xJnqwJI7KoF4iaUJ7EeFQNJawjUXT8X1rvbLMUk/O/fgrzAfhD163XuckA==}

  '@iconify/types@2.0.0':
    resolution: {integrity: sha512-+wluvCrRhXrhyOmRDJ3q8mux9JkKy5SJ/v8ol2tu4FVjyYvtEzkc/3pK15ET6RKg4b4w4BmTk1+gsCUhf21Ykg==}

  '@iconify/utils@2.1.33':
    resolution: {integrity: sha512-jP9h6v/g0BIZx0p7XGJJVtkVnydtbgTgt9mVNcGDYwaa7UhdHdI9dvoq+gKj9sijMSJKxUPEG2JyjsgXjxL7Kw==}

  '@iconify/vue@4.2.0':
    resolution: {integrity: sha512-CMynoz9BDWugDO2B7LU/s8L99dHCiqDGCjCki6bhVx5etZhw9x0BTV7wWRdj82jtl1yQTc+QQRcHQmSvUY6R+g==}
    peerDependencies:
      vue: '>=3'

  '@intlify/core-base@10.0.5':
    resolution: {integrity: sha512-F3snDTQs0MdvnnyzTDTVkOYVAZOE/MHwRvF7mn7Jw1yuih4NrFYLNYIymGlLmq4HU2iIdzYsZ7f47bOcwY73XQ==}
    engines: {node: '>= 16'}

  '@intlify/message-compiler@10.0.5':
    resolution: {integrity: sha512-6GT1BJ852gZ0gItNZN2krX5QAmea+cmdjMvsWohArAZ3GmHdnNANEcF9JjPXAMRtQ6Ux5E269ymamg/+WU6tQA==}
    engines: {node: '>= 16'}

  '@intlify/shared@10.0.5':
    resolution: {integrity: sha512-bmsP4L2HqBF6i6uaMqJMcFBONVjKt+siGluRq4Ca4C0q7W2eMaVZr8iCgF9dKbcVXutftkC7D6z2SaSMmLiDyA==}
    engines: {node: '>= 16'}

  '@jridgewell/gen-mapping@0.3.5':
    resolution: {integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/source-map@0.3.6':
    resolution: {integrity: sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@nuxt/kit@3.14.1592':
    resolution: {integrity: sha512-r9r8bISBBisvfcNgNL3dSIQHSBe0v5YkX5zwNblIC2T0CIEgxEVoM5rq9O5wqgb5OEydsHTtT2hL57vdv6VT2w==}
    engines: {node: ^14.18.0 || >=16.10.0}

  '@nuxt/schema@3.14.1592':
    resolution: {integrity: sha512-A1d/08ueX8stTXNkvGqnr1eEXZgvKn+vj6s7jXhZNWApUSqMgItU4VK28vrrdpKbjIPwq2SwhnGOHUYvN9HwCQ==}
    engines: {node: ^14.18.0 || >=16.10.0}

  '@parcel/watcher-android-arm64@2.5.0':
    resolution: {integrity: sha512-qlX4eS28bUcQCdribHkg/herLe+0A9RyYC+mm2PXpncit8z5b3nSqGVzMNR3CmtAOgRutiZ02eIJJgP/b1iEFQ==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [android]

  '@parcel/watcher-darwin-arm64@2.5.0':
    resolution: {integrity: sha512-hyZ3TANnzGfLpRA2s/4U1kbw2ZI4qGxaRJbBH2DCSREFfubMswheh8TeiC1sGZ3z2jUf3s37P0BBlrD3sjVTUw==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [darwin]

  '@parcel/watcher-darwin-x64@2.5.0':
    resolution: {integrity: sha512-9rhlwd78saKf18fT869/poydQK8YqlU26TMiNg7AIu7eBp9adqbJZqmdFOsbZ5cnLp5XvRo9wcFmNHgHdWaGYA==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [darwin]

  '@parcel/watcher-freebsd-x64@2.5.0':
    resolution: {integrity: sha512-syvfhZzyM8kErg3VF0xpV8dixJ+RzbUaaGaeb7uDuz0D3FK97/mZ5AJQ3XNnDsXX7KkFNtyQyFrXZzQIcN49Tw==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [freebsd]

  '@parcel/watcher-linux-arm-glibc@2.5.0':
    resolution: {integrity: sha512-0VQY1K35DQET3dVYWpOaPFecqOT9dbuCfzjxoQyif1Wc574t3kOSkKevULddcR9znz1TcklCE7Ht6NIxjvTqLA==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]
    libc: [glibc]

  '@parcel/watcher-linux-arm-musl@2.5.0':
    resolution: {integrity: sha512-6uHywSIzz8+vi2lAzFeltnYbdHsDm3iIB57d4g5oaB9vKwjb6N6dRIgZMujw4nm5r6v9/BQH0noq6DzHrqr2pA==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]
    libc: [musl]

  '@parcel/watcher-linux-arm64-glibc@2.5.0':
    resolution: {integrity: sha512-BfNjXwZKxBy4WibDb/LDCriWSKLz+jJRL3cM/DllnHH5QUyoiUNEp3GmL80ZqxeumoADfCCP19+qiYiC8gUBjA==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@parcel/watcher-linux-arm64-musl@2.5.0':
    resolution: {integrity: sha512-S1qARKOphxfiBEkwLUbHjCY9BWPdWnW9j7f7Hb2jPplu8UZ3nes7zpPOW9bkLbHRvWM0WDTsjdOTUgW0xLBN1Q==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@parcel/watcher-linux-x64-glibc@2.5.0':
    resolution: {integrity: sha512-d9AOkusyXARkFD66S6zlGXyzx5RvY+chTP9Jp0ypSTC9d4lzyRs9ovGf/80VCxjKddcUvnsGwCHWuF2EoPgWjw==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@parcel/watcher-linux-x64-musl@2.5.0':
    resolution: {integrity: sha512-iqOC+GoTDoFyk/VYSFHwjHhYrk8bljW6zOhPuhi5t9ulqiYq1togGJB5e3PwYVFFfeVgc6pbz3JdQyDoBszVaA==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@parcel/watcher-win32-arm64@2.5.0':
    resolution: {integrity: sha512-twtft1d+JRNkM5YbmexfcH/N4znDtjgysFaV9zvZmmJezQsKpkfLYJ+JFV3uygugK6AtIM2oADPkB2AdhBrNig==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [win32]

  '@parcel/watcher-win32-ia32@2.5.0':
    resolution: {integrity: sha512-+rgpsNRKwo8A53elqbbHXdOMtY/tAtTzManTWShB5Kk54N8Q9mzNWV7tV+IbGueCbcj826MfWGU3mprWtuf1TA==}
    engines: {node: '>= 10.0.0'}
    cpu: [ia32]
    os: [win32]

  '@parcel/watcher-win32-x64@2.5.0':
    resolution: {integrity: sha512-lPrxve92zEHdgeff3aiu4gDOIt4u7sJYha6wbdEZDCDUhtjTsOMiaJzG5lMY4GkWH8p0fMmO2Ppq5G5XXG+DQw==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [win32]

  '@parcel/watcher@2.5.0':
    resolution: {integrity: sha512-i0GV1yJnm2n3Yq1qw6QrUrd/LI9bE8WEBOTtOkpCXHHdyN3TAGgqAK/DAT05z4fq2x04cARXt2pDmjWjL92iTQ==}
    engines: {node: '>= 10.0.0'}

  '@pkgr/core@0.1.1':
    resolution: {integrity: sha512-cq8o4cWH0ibXh9VGi5P20Tu9XF/0fFXl9EUinr9QfTM7a7p0oTA4iJRCQWppXR1Pg8dSM0UCItCkPwsk9qWWYA==}
    engines: {node: ^12.20.0 || ^14.18.0 || >=16.0.0}

  '@polka/url@1.0.0-next.28':
    resolution: {integrity: sha512-8LduaNlMZGwdZ6qWrKlfa+2M4gahzFkprZiAt2TF8uS0qQgBizKXpXURqvTJ4WtmupWxaLqjRb2UCTe72mu+Aw==}

  '@popperjs/core@2.11.8':
    resolution: {integrity: sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==}

  '@remirror/core-constants@3.0.0':
    resolution: {integrity: sha512-42aWfPrimMfDKDi4YegyS7x+/0tlzaqwPQCULLanv3DMIlu96KTJR0fM5isWX2UViOqlGnX6YFgqWepcX+XMNg==}

  '@rollup/pluginutils@5.1.3':
    resolution: {integrity: sha512-Pnsb6f32CD2W3uCaLZIzDmeFyQ2b8UWMFI7xtwUezpcGBDVDW6y9XgAWIlARiGAo6eNF5FK5aQTr0LFyNyqq5A==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/rollup-android-arm-eabi@4.28.0':
    resolution: {integrity: sha512-wLJuPLT6grGZsy34g4N1yRfYeouklTgPhH1gWXCYspenKYD0s3cR99ZevOGw5BexMNywkbV3UkjADisozBmpPQ==}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.28.0':
    resolution: {integrity: sha512-eiNkznlo0dLmVG/6wf+Ifi/v78G4d4QxRhuUl+s8EWZpDewgk7PX3ZyECUXU0Zq/Ca+8nU8cQpNC4Xgn2gFNDA==}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.28.0':
    resolution: {integrity: sha512-lmKx9yHsppblnLQZOGxdO66gT77bvdBtr/0P+TPOseowE7D9AJoBw8ZDULRasXRWf1Z86/gcOdpBrV6VDUY36Q==}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.28.0':
    resolution: {integrity: sha512-8hxgfReVs7k9Js1uAIhS6zq3I+wKQETInnWQtgzt8JfGx51R1N6DRVy3F4o0lQwumbErRz52YqwjfvuwRxGv1w==}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.28.0':
    resolution: {integrity: sha512-lA1zZB3bFx5oxu9fYud4+g1mt+lYXCoch0M0V/xhqLoGatbzVse0wlSQ1UYOWKpuSu3gyN4qEc0Dxf/DII1bhQ==}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.28.0':
    resolution: {integrity: sha512-aI2plavbUDjCQB/sRbeUZWX9qp12GfYkYSJOrdYTL/C5D53bsE2/nBPuoiJKoWp5SN78v2Vr8ZPnB+/VbQ2pFA==}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.28.0':
    resolution: {integrity: sha512-WXveUPKtfqtaNvpf0iOb0M6xC64GzUX/OowbqfiCSXTdi/jLlOmH0Ba94/OkiY2yTGTwteo4/dsHRfh5bDCZ+w==}
    cpu: [arm]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm-musleabihf@4.28.0':
    resolution: {integrity: sha512-yLc3O2NtOQR67lI79zsSc7lk31xjwcaocvdD1twL64PK1yNaIqCeWI9L5B4MFPAVGEVjH5k1oWSGuYX1Wutxpg==}
    cpu: [arm]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-arm64-gnu@4.28.0':
    resolution: {integrity: sha512-+P9G9hjEpHucHRXqesY+3X9hD2wh0iNnJXX/QhS/J5vTdG6VhNYMxJ2rJkQOxRUd17u5mbMLHM7yWGZdAASfcg==}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm64-musl@4.28.0':
    resolution: {integrity: sha512-1xsm2rCKSTpKzi5/ypT5wfc+4bOGa/9yI/eaOLW0oMs7qpC542APWhl4A37AENGZ6St6GBMWhCCMM6tXgTIplw==}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-powerpc64le-gnu@4.28.0':
    resolution: {integrity: sha512-zgWxMq8neVQeXL+ouSf6S7DoNeo6EPgi1eeqHXVKQxqPy1B2NvTbaOUWPn/7CfMKL7xvhV0/+fq/Z/J69g1WAQ==}
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-gnu@4.28.0':
    resolution: {integrity: sha512-VEdVYacLniRxbRJLNtzwGt5vwS0ycYshofI7cWAfj7Vg5asqj+pt+Q6x4n+AONSZW/kVm+5nklde0qs2EUwU2g==}
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-s390x-gnu@4.28.0':
    resolution: {integrity: sha512-LQlP5t2hcDJh8HV8RELD9/xlYtEzJkm/aWGsauvdO2ulfl3QYRjqrKW+mGAIWP5kdNCBheqqqYIGElSRCaXfpw==}
    cpu: [s390x]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-gnu@4.28.0':
    resolution: {integrity: sha512-Nl4KIzteVEKE9BdAvYoTkW19pa7LR/RBrT6F1dJCV/3pbjwDcaOq+edkP0LXuJ9kflW/xOK414X78r+K84+msw==}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-musl@4.28.0':
    resolution: {integrity: sha512-eKpJr4vBDOi4goT75MvW+0dXcNUqisK4jvibY9vDdlgLx+yekxSm55StsHbxUsRxSTt3JEQvlr3cGDkzcSP8bw==}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-win32-arm64-msvc@4.28.0':
    resolution: {integrity: sha512-Vi+WR62xWGsE/Oj+mD0FNAPY2MEox3cfyG0zLpotZdehPFXwz6lypkGs5y38Jd/NVSbOD02aVad6q6QYF7i8Bg==}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.28.0':
    resolution: {integrity: sha512-kN/Vpip8emMLn/eOza+4JwqDZBL6MPNpkdaEsgUtW1NYN3DZvZqSQrbKzJcTL6hd8YNmFTn7XGWMwccOcJBL0A==}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.28.0':
    resolution: {integrity: sha512-Bvno2/aZT6usSa7lRDL2+hMjVAGjuqaymF1ApZm31JXzniR/hvr14jpU+/z4X6Gt5BPlzosscyJZGUvguXIqeQ==}
    cpu: [x64]
    os: [win32]

  '@sec-ant/readable-stream@0.4.1':
    resolution: {integrity: sha512-831qok9r2t8AlxLko40y2ebgSDhenenCatLVeW/uBtnHPyhHOvG0C7TvfgecV+wHzIm5KUICgzmVpWS+IMEAeg==}

  '@shikijs/core@1.24.0':
    resolution: {integrity: sha512-6pvdH0KoahMzr6689yh0QJ3rCgF4j1XsXRHNEeEN6M4xJTfQ6QPWrmHzIddotg+xPJUPEPzYzYCKzpYyhTI6Gw==}

  '@shikijs/engine-javascript@1.24.0':
    resolution: {integrity: sha512-ZA6sCeSsF3Mnlxxr+4wGEJ9Tto4RHmfIS7ox8KIAbH0MTVUkw3roHPHZN+LlJMOHJJOVupe6tvuAzRpN8qK1vA==}

  '@shikijs/engine-oniguruma@1.24.0':
    resolution: {integrity: sha512-Eua0qNOL73Y82lGA4GF5P+G2+VXX9XnuUxkiUuwcxQPH4wom+tE39kZpBFXfUuwNYxHSkrSxpB1p4kyRW0moSg==}

  '@shikijs/transformers@1.24.0':
    resolution: {integrity: sha512-Qf/hby+PRPkoHncjYnJf5svK1aCsOUtQhuLzKPnmeXJtuUZCmbH0pTpdNtXe9tgln/RHlyRJnv7q46HHS1sO0Q==}

  '@shikijs/types@1.24.0':
    resolution: {integrity: sha512-aptbEuq1Pk88DMlCe+FzXNnBZ17LCiLIGWAeCWhoFDzia5Q5Krx3DgnULLiouSdd6+LUM39XwXGppqYE0Ghtug==}

  '@shikijs/vscode-textmate@9.3.0':
    resolution: {integrity: sha512-jn7/7ky30idSkd/O5yDBfAnVt+JJpepofP/POZ1iMOxK59cOfqIgg/Dj0eFsjOTMw+4ycJN0uhZH/Eb0bs/EUA==}

  '@sindresorhus/merge-streams@2.3.0':
    resolution: {integrity: sha512-LtoMMhxAlorcGhmFYI+LhPgbPZCkgP6ra1YL604EeF6U98pLlQ3iWIGMdWSC+vWmPBWBNgmDBAhnAobLROJmwg==}
    engines: {node: '>=18'}

  '@sindresorhus/merge-streams@4.0.0':
    resolution: {integrity: sha512-tlqY9xq5ukxTUZBmoOp+m61cqwQD5pHJtFY3Mn8CA8ps6yghLH/Hw8UPdqg4OLmFW3IFlcXnQNmo/dh8HzXYIQ==}
    engines: {node: '>=18'}

  '@tiptap/core@2.10.3':
    resolution: {integrity: sha512-wAG/0/UsLeZLmshWb6rtWNXKJftcmnned91/HLccHVQAuQZ1UWH+wXeQKu/mtodxEO7JcU2mVPR9mLGQkK0McQ==}
    peerDependencies:
      '@tiptap/pm': ^2.7.0

  '@tiptap/core@2.26.1':
    resolution: {integrity: sha512-fymyd/XZvYiHjBoLt1gxs024xP/LY26d43R1vluYq7AHBL/7DE3ywzy+1GEsGyAv5Je2L0KBhNIR/izbq3Kaqg==}
    peerDependencies:
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-bold@2.10.3':
    resolution: {integrity: sha512-xnF1tS2BsORenr11qyybW120gHaeHKiKq+ZOP14cGA0MsriKvWDnaCSocXP/xMEYHy7+2uUhJ0MsKkHVj4bPzQ==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-bubble-menu@2.10.3':
    resolution: {integrity: sha512-e9a4yMjQezuKy0rtyyzxbV2IAE1bm1PY3yoZEFrcaY0o47g1CMUn2Hwe+9As2HdntEjQpWR7NO1mZeKxHlBPYA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-bubble-menu@3.0.7':
    resolution: {integrity: sha512-/oL5kgOHm1AJtyLC6v1+txk/RI9WvI4/gDQ6oWukmT7aQHIfqvCW0DN/ahmX9nxGFAIRlbrooVxLn5Y6/P0adQ==}
    peerDependencies:
      '@tiptap/core': ^3.0.7
      '@tiptap/pm': ^3.0.7

  '@tiptap/extension-bullet-list@2.10.3':
    resolution: {integrity: sha512-PTkwJOVlHi4RR4Wrs044tKMceweXwNmWA6EoQ93hPUVtQcwQL990Es5Izp+i88twTPLuGD9dH+o9QDyH9SkWdA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-character-count@2.10.3':
    resolution: {integrity: sha512-7L3VS9+SZqwK94/Yk4c+NEpI6kDUAYW3tYGuxCRiKHDlUy3fkXkVkPlxsoNUpAA4O05KGAwD0YW5rvAkNXdi8w==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-code-block@2.10.3':
    resolution: {integrity: sha512-yiDVNg22fYkzsFk5kBlDSHcjwVJgajvO/M5fDXA+Hfxwo2oNcG6aJyyHXFe+UaXTVjdkPej0J6kcMKrTMCiFug==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-code@2.10.3':
    resolution: {integrity: sha512-JyLbfyY3cPctq9sVdpcRWTcoUOoq3/MnGE1eP6eBNyMTHyBPcM9TPhOkgj+xkD1zW/884jfelB+wa70RT/AMxQ==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-collaboration-cursor@2.22.3':
    resolution: {integrity: sha512-+2O7sBlW3WA7ytFBrAUnGUK00ua1sKI+zhdeGt3w/tLDKZoG7EStDYAuNc+1ffpDy9pEYgvwpazNxlsmRLug8w==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      y-prosemirror: ^1.2.11

  '@tiptap/extension-collaboration@2.22.3':
    resolution: {integrity: sha512-8zSgZ7V3vA3XPuGvXMaL0S/eU84FdKq0BsiGjs0BeLi455wR/O89Vb9BiwcmtosN5GfpNQpsP/pZ44e5Ny4saQ==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0
      y-prosemirror: ^1.2.11

  '@tiptap/extension-document@2.10.3':
    resolution: {integrity: sha512-6i8+xbS2zB6t8iFzli1O/QB01MmwyI5Hqiiv4m5lOxqavmJwLss2sRhoMC2hB3CyFg5UmeODy/f/RnI6q5Vixg==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-dropcursor@2.10.3':
    resolution: {integrity: sha512-wzWf82ixWzZQr0hxcf/A0ul8NNxgy1N63O+c56st6OomoLuKUJWOXF+cs9O7V+/5rZKWdbdYYoRB5QLvnDBAlQ==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-floating-menu@3.0.7':
    resolution: {integrity: sha512-JJv9pV8EwTcGe2w/1hMhjAhfmvoCh8ha3Rh/9soWfe8FfwRnQQC6ykqmYWuAx1HDoS+sNYPNUbyDxIwgnbIc+w==}
    peerDependencies:
      '@floating-ui/dom': ^1.0.0
      '@tiptap/core': ^3.0.7
      '@tiptap/pm': ^3.0.7

  '@tiptap/extension-gapcursor@2.10.3':
    resolution: {integrity: sha512-FskZi2DqDSTH1WkgLF2OLy0xU7qj3AgHsKhVsryeAtld4jAK5EsonneWgaipbz0e/MxuIvc1oyacfZKABpLaNg==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-image@2.22.3':
    resolution: {integrity: sha512-JO8n5YOqOs+bckPZZ3qJFFLpRbYlu4N52n/7Do0XmxEMWaa3fLcR0Rsa1v3X4dGH2T5cKQ475dWSpJQRc+x07w==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-italic@2.10.3':
    resolution: {integrity: sha512-wAiO6ZxoHx2H90phnKttLWGPjPZXrfKxhOCsqYrK8BpRByhr48godOFRuGwYnKaiwoVjpxc63t+kDJDWvqmgMw==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-link@2.10.3':
    resolution: {integrity: sha512-8esKlkZBzEiNcpt7I8Cd6l1mWmCc/66pPbUq9LfnIniDXE3U+ahBf4m3TJltYFBGbiiTR/xqMtJyVHOpuLDtAw==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-list-item@2.10.3':
    resolution: {integrity: sha512-9sok81gvZfSta2K1Dwrq5/HSz1jk4zHBpFqCx0oydzodGslx6X1bNxdca+eXJpXZmQIWALK7zEr4X8kg3WZsgw==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-mention@3.0.7':
    resolution: {integrity: sha512-PHEx6NdmarjvPPvTd8D9AqK1JIaVYTsnQLxJUERakOLzujgUCToZ7FpMQDhPj97YLvF0t3jeyjZOPmFuj5kw4w==}
    peerDependencies:
      '@tiptap/core': ^3.0.7
      '@tiptap/pm': ^3.0.7
      '@tiptap/suggestion': ^3.0.7

  '@tiptap/extension-ordered-list@2.10.3':
    resolution: {integrity: sha512-/SFuEDnbJxy3jvi72LeyiPHWkV+uFc0LUHTUHSh20vwyy+tLrzncJfXohGbTIv5YxYhzExQYZDRD4VbSghKdlw==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-placeholder@2.10.3':
    resolution: {integrity: sha512-0OkwnDLguZgoiJM85cfnOySuMmPUF7qqw7DHQ+c3zwTAYnvzpvqrvpupc+2Zi9GfC1sDgr+Ajrp8imBHa6PHfA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-strike@2.10.3':
    resolution: {integrity: sha512-jYoPy6F6njYp3txF3u23bgdRy/S5ATcWDO9LPZLHSeikwQfJ47nqb+EUNo5M8jIOgFBTn4MEbhuZ6OGyhnxopA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-subscript@2.10.3':
    resolution: {integrity: sha512-GkOwXIruM7QksmlfqLTKTC6JBpWSBDN2eeoPwggxXuqetqYs4sIx1ul3LEGDQy0vglcFKGkbbO2IiHCO/0fSWA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-superscript@2.10.3':
    resolution: {integrity: sha512-4bXDPyT10ByVCLXFR8A70TcpFJ0H3PicRsxKJcQ+KZIauNUo5BBUpkF2cK+IOUp4UZ1W5ZBeuMQG5HWMuV9T1A==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-table-cell@2.10.3':
    resolution: {integrity: sha512-EYzBrnq7KUAcRhshIoTmC4ED8YoF4Ei5m8ZMPOctKX+QMAagKdcrw2UxuOf4tP2xgBYx+qDsKCautepZXQiL2g==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-table-header@2.10.3':
    resolution: {integrity: sha512-zJqzivz+VITYIFXNH09leBbkwAPuvp504rCAFL2PMa1uaME6+oiiRqZvXQrOiRkjNpOWEXH4dqvVLwkSMZoWaw==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-table-row@2.10.3':
    resolution: {integrity: sha512-l6P6BAE4SuIFdPmsRd+zGP2Ks9AhLAua7nfDlHFMWDnfOeaJu7g/t4oG++9xTojDcVDHhcIe8TJYUXfhOt2anw==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-table@2.10.3':
    resolution: {integrity: sha512-XAvq0ptpHfuN7lQhTeew4Sqo8aKYHTqroa7cHL8I+gWJqYqKJSTGb4FAqdGIFEzHvnSsMCFbTL//kAHXvTdsHg==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-task-item@2.10.3':
    resolution: {integrity: sha512-vE4qxGrZTdwynHq6l5xN0jI0ahDZpmKeoD6yuCMNyN831dgHXEjNrV8oBtZUvvqChFRc/LiSmUbrTInUn5xeNg==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-task-list@2.10.3':
    resolution: {integrity: sha512-Zj1pj+6VrL8VXlFYWdcLlCMykzARsvdqdU8cGVnBuC0H0vrSSfLGl+GxGnQwxTnqiNtxR4t70DLi/UjFBvzlqw==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-text@2.10.3':
    resolution: {integrity: sha512-7p9XiRprsRZm8y9jvF/sS929FCELJ5N9FQnbzikOiyGNUx5mdI+exVZlfvBr9xOD5s7fBLg6jj9Vs0fXPNRkPg==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-typography@2.10.3':
    resolution: {integrity: sha512-lLUm6PSufACffAFQaK3bwoM3nFlQ/RdG21a3rKOoLWh+abYvIZ8UilYgebH9r2+DBET6UrG7I/0mBtm+L/Lheg==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-underline@2.10.3':
    resolution: {integrity: sha512-VeGs0jeNiTnXddHHJEgOc/sKljZiyTEgSSuqMmsBACrr9aGFXbLTgKTvNjkZ9WzSnu7LwgJuBrwEhg8yYixUyQ==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/pm@2.10.3':
    resolution: {integrity: sha512-771p53aU0KFvujvKpngvq2uAxThlEsjYaXcVVmwrhf0vxSSg+psKQEvqvWvHv/3BwkPVCGwmEKNVJZjaXFKu4g==}

  '@tiptap/suggestion@2.10.3':
    resolution: {integrity: sha512-ReEwiPQoDTXn3RuWnj9D7Aod9dbNQz0QAoLRftWUTdbj3O2ohbvTNX6tlcfS+7x48Q+fAALiJGpp5BtctODlsA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/vue-3@3.0.7':
    resolution: {integrity: sha512-ov0wztf/UeOFtZfTSFdLEVSvnJ+cdP5ZUwiibCmQYRyuN6731T/YZrCl4I0HJlNKAYvJhMyhb+sp+9aOC2K/Rw==}
    peerDependencies:
      '@floating-ui/dom': ^1.0.0
      '@tiptap/core': ^3.0.7
      '@tiptap/pm': ^3.0.7
      vue: ^3.0.0

  '@types/eslint@9.6.1':
    resolution: {integrity: sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag==}

  '@types/estree@1.0.6':
    resolution: {integrity: sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==}

  '@types/hast@3.0.4':
    resolution: {integrity: sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/linkify-it@3.0.5':
    resolution: {integrity: sha512-yg6E+u0/+Zjva+buc3EIb+29XEg4wltq7cSmd4Uc2EE/1nUVmxyzpX6gUXD0V8jIrG0r7YeOGVIbYRkxeooCtw==}

  '@types/linkify-it@5.0.0':
    resolution: {integrity: sha512-sVDA58zAw4eWAffKOaQH5/5j3XeayukzDk+ewSsnv3p4yJEZHCCzMDiZM8e0OUrRvmpGZ85jf4yDHkHsgBNr9Q==}

  '@types/markdown-it@13.0.9':
    resolution: {integrity: sha512-1XPwR0+MgXLWfTn9gCsZ55AHOKW1WN+P9vr0PaQh5aerR9LLQXUbjfEAFhjmEmyoYFWAyuN2Mqkn40MZ4ukjBw==}

  '@types/markdown-it@14.1.2':
    resolution: {integrity: sha512-promo4eFwuiW+TfGxhi+0x3czqTYJkG8qB17ZUJiVF10Xm7NLVRSLUsfRTU/6h1e24VvRnXCx+hG7li58lkzog==}

  '@types/mdast@4.0.4':
    resolution: {integrity: sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==}

  '@types/mdurl@1.0.5':
    resolution: {integrity: sha512-6L6VymKTzYSrEf4Nev4Xa1LCHKrlTlYCBMTlQKFuddo1CvQcE52I0mwfOJayueUC7MJuXOeHTcIU683lzd0cUA==}

  '@types/mdurl@2.0.0':
    resolution: {integrity: sha512-RGdgjQUZba5p6QEFAVx2OGb8rQDL/cPRG7GiedRzMcJ1tYnUANBncjbSB1NRGwbvjcPeikRABz2nshyPk1bhWg==}

  '@types/node@22.10.1':
    resolution: {integrity: sha512-qKgsUwfHZV2WCWLAnVP1JqnpE6Im6h3Y0+fYgMTasNQ7V++CBX5OT1as0g0f+OyubbFqhf6XVNIsmN4IIhEgGQ==}

  '@types/unist@3.0.3':
    resolution: {integrity: sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q==}

  '@types/web-bluetooth@0.0.20':
    resolution: {integrity: sha512-g9gZnnXVq7gM7v3tJCWV/qw7w+KeOlSHAhgF9RytFyifW6AF61hdT2ucrYhPq9hLs5JIryeupHV3qGk95dH9ow==}

  '@ungap/structured-clone@1.2.0':
    resolution: {integrity: sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ==}

  '@unocss/astro@0.63.6':
    resolution: {integrity: sha512-5Fjlv6dpQo6o2PUAcEv8p24G8rn8Op79xLFofq2V+iA/Q32G9/UsxTLOpj+yc+q0YdJrFfDCT2X/3pvVY8Db5g==}
    peerDependencies:
      vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0
    peerDependenciesMeta:
      vite:
        optional: true

  '@unocss/cli@0.63.6':
    resolution: {integrity: sha512-OZb8hO0x4nCJjFd3Gq3km78YnyMAdq282D+BLiDE6IhQ5WHCVL7fyhfgIVL6xwxISDVxiyITwNb72ky0MEutPg==}
    engines: {node: '>=14'}
    hasBin: true

  '@unocss/config@0.63.6':
    resolution: {integrity: sha512-+4Lt5uTwRgu1z7vhOUzDf+mL+BQYdaa/Z8NMT2Fiqb37tcjEKvmwaUHdfE22Vif1luDgC6xqFsn6qqFtOxhoWQ==}
    engines: {node: '>=14'}

  '@unocss/core@0.63.6':
    resolution: {integrity: sha512-Q4QPgJ271Up89+vIqqOKgtdCKkFpHqvHN8W1LUlKPqtYnOvVYaOIVNAZowaIdEhPuc83yLc6Tg2+7riK18QKEw==}

  '@unocss/extractor-arbitrary-variants@0.63.6':
    resolution: {integrity: sha512-HJX0oAa9uzwKYoU8CoJdP1gxjuqFmOLxyZmITjStAmZNZpIxlz2wz4VrHmqml2dkvx/mifGGGc/GxZpQ36D12Q==}

  '@unocss/inspector@0.63.6':
    resolution: {integrity: sha512-DQDJnhtzdHIQXD2vCdj5ytFnHfQCWJGPmrHJHXxzkTYn8nIovV1roVl1ITLxkDIIYK9bdYneg2imQN5JCZhHmQ==}

  '@unocss/postcss@0.63.6':
    resolution: {integrity: sha512-XI6U1jMwbQoSHVWpZZu3Cxp3t1PVj5VOj+IYtz7xmcWP9GVK+eyETo/xyB0l4muD4emXfSrhNDrFYzSyIyF5cg==}
    engines: {node: '>=14'}
    peerDependencies:
      postcss: ^8.4.21

  '@unocss/preset-attributify@0.63.6':
    resolution: {integrity: sha512-sHH17mfl/THHLxCLAHqPdUniCNMFjAxBHFDZYgGi83azuarF2evI5Mtc3Qsj3nzoSQwTPmK2VY3XYUUrpPDGWQ==}

  '@unocss/preset-icons@0.63.6':
    resolution: {integrity: sha512-fRU44wXABnMPT/9zhKBNSUeDJlOxJhUJP9W3FSRnc+ktjAifJIj0xpUKtEqxL46QMq825Bsj2yDSquzP+XYGnQ==}

  '@unocss/preset-mini@0.63.6':
    resolution: {integrity: sha512-pZDZbSuxabHSwPIy3zCgQ4MNdVCSHvOvZecreH+v96R1oOhquiwU8WiSbkxvZiKiLQJd7JUVW87E1pAzr5ZGGQ==}

  '@unocss/preset-tagify@0.63.6':
    resolution: {integrity: sha512-3lKhk4MW3RqJBwIvBXHj0H0/kHkFlKtCIBQFiBcCJh8TXOID8IZ0iVjuGwdlk63VTizI/wnsNDOVpj6YcjRRlw==}

  '@unocss/preset-typography@0.63.6':
    resolution: {integrity: sha512-AXmBVnbV54gUIv5kbywjZek9ZlKRwJfBDVMtWOcLOjN3AHirGx1W2oq2UzNkfYZ2leof/Y2BocxeTwGCCRhqDQ==}

  '@unocss/preset-uno@0.63.6':
    resolution: {integrity: sha512-67PbHyVgAe9Rz0Rhyl3zBibFuGmqQMRPMkRjNYrwmmtNydpQYsXbfnDs0p8mZFp6uO2o3Jkh7urqEtixHHvq0Q==}

  '@unocss/preset-web-fonts@0.63.6':
    resolution: {integrity: sha512-ko1aHDax0u5CQi1BXggv6uW5Vq/LQRWwzOxqBFTh1JlGHPZTw4CdVJkYnlpt3WEW+FPUzZYjhKmMmQY7KtOTng==}

  '@unocss/preset-wind@0.63.6':
    resolution: {integrity: sha512-W3oZ2TXSqStNE+X++kcspRTF2Szu2ej6NW5Kiyy6WQn/+ZD77AF4VtvzHtzFVZ2QKpEIovGBpU5tywooHbB7hw==}

  '@unocss/reset@0.63.6':
    resolution: {integrity: sha512-gq73RSZj54MOloqrivkoMPXCqNG2WpIyBT1AYlF76uKxEEbUD41E8uBUhLSKs7gFgF01yQJLRaIuyN1yw09pbQ==}

  '@unocss/rule-utils@0.63.6':
    resolution: {integrity: sha512-moeDEq5d9mB8gSYeoqHMkXWWekaFFdhg7QCuwwCbxCc+NPMOgGkmfAoafz+y2tdvK7pEuT191RWOiHQ0MkA5oQ==}
    engines: {node: '>=14'}

  '@unocss/transformer-attributify-jsx@0.63.6':
    resolution: {integrity: sha512-/RU09MF+hJK7cFbLJ+8vloCGyhn6Oys8R6gey0auB0+nw/ucUXoLQKWgUqo9taQlLuYOiehdkYjQSdWn5lyA/Q==}

  '@unocss/transformer-compile-class@0.63.6':
    resolution: {integrity: sha512-zzAqs8adnTUOLA88RgcToadcrz9gjxrZk6IrcmMqMmWqk0MOWNQHIN0RzKa/yaw4QhO2xuGyIz4/WHyXuCXMQg==}

  '@unocss/transformer-directives@0.63.6':
    resolution: {integrity: sha512-XcNOwLRbfrJSU6YXyLgiMzAigSzjIdvHwS3lLCZ2n6DWuLmTuXBfvVtRxeJ+aflNkhpQNKONCClC4s6I2r53uw==}

  '@unocss/transformer-variant-group@0.63.6':
    resolution: {integrity: sha512-ebYSjZnZrtcJYjmAEDwGVwPuaQ9EVWKNDDJFFSusP8k/6PjJoHDh0qkj+hdPPDhYn81yzJQalU1eSUSlfC30VA==}

  '@unocss/vite@0.63.6':
    resolution: {integrity: sha512-gxK3gtvYQH5S/qtuvsY4M0S+KJPZnYrOQI/Gopufx+b2qgmwZ/TSAe66gWeKYfe3DfQsmA3PPh/GXpkK+/FnHg==}
    peerDependencies:
      vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0

  '@vitejs/plugin-vue-jsx@4.1.1':
    resolution: {integrity: sha512-uMJqv/7u1zz/9NbWAD3XdjaY20tKTf17XVfQ9zq4wY1BjsB/PjpJPMe2xiG39QpP4ZdhYNhm4Hvo66uJrykNLA==}
    engines: {node: ^18.0.0 || >=20.0.0}
    peerDependencies:
      vite: ^5.0.0 || ^6.0.0
      vue: ^3.0.0

  '@vitejs/plugin-vue@5.2.1':
    resolution: {integrity: sha512-cxh314tzaWwOLqVes2gnnCtvBDcM1UMdn+iFR+UjAn411dPT3tOmqrJjbMd7koZpMAmBM/GqeV4n9ge7JSiJJQ==}
    engines: {node: ^18.0.0 || >=20.0.0}
    peerDependencies:
      vite: ^5.0.0 || ^6.0.0
      vue: ^3.2.25

  '@vitepress-demo-preview/component@2.3.2':
    resolution: {integrity: sha512-+GCYl3eHA7uoBGZyH2nTP1uab6pHFGEdykj5bSz4Nt2carH05xomBf18kBecuK1SncSqB7kWV/6JHT2Y349NbQ==}
    peerDependencies:
      vitepress: '*'
      vue: ^3.2.0

  '@vitepress-demo-preview/plugin@1.2.3':
    resolution: {integrity: sha512-uUb6UNkcRZnA+bwIEJV9fnpwnF6v3bow+9iQAOgFWdcwiII6/kT2c21RYe8ywmoIlBkniJmlqo+qeU4Mi8rx/Q==}
    peerDependencies:
      markdown-it-container: ^3.0.0
      vitepress: '*'
      vue: ^3.2.0

  '@vue/babel-helper-vue-transform-on@1.2.5':
    resolution: {integrity: sha512-lOz4t39ZdmU4DJAa2hwPYmKc8EsuGa2U0L9KaZaOJUt0UwQNjNA3AZTq6uEivhOKhhG1Wvy96SvYBoFmCg3uuw==}

  '@vue/babel-plugin-jsx@1.2.5':
    resolution: {integrity: sha512-zTrNmOd4939H9KsRIGmmzn3q2zvv1mjxkYZHgqHZgDrXz5B1Q3WyGEjO2f+JrmKghvl1JIRcvo63LgM1kH5zFg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    peerDependenciesMeta:
      '@babel/core':
        optional: true

  '@vue/babel-plugin-resolve-type@1.2.5':
    resolution: {integrity: sha512-U/ibkQrf5sx0XXRnUZD1mo5F7PkpKyTbfXM3a3rC4YnUz6crHEz9Jg09jzzL6QYlXNto/9CePdOg/c87O4Nlfg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@vue/compiler-core@3.5.13':
    resolution: {integrity: sha512-oOdAkwqUfW1WqpwSYJce06wvt6HljgY3fGeM9NcVA1HaYOij3mZG9Rkysn0OHuyUAGMbEbARIpsG+LPVlBJ5/Q==}

  '@vue/compiler-dom@3.5.13':
    resolution: {integrity: sha512-ZOJ46sMOKUjO3e94wPdCzQ6P1Lx/vhp2RSvfaab88Ajexs0AHeV0uasYhi99WPaogmBlRHNRuly8xV75cNTMDA==}

  '@vue/compiler-sfc@3.5.13':
    resolution: {integrity: sha512-6VdaljMpD82w6c2749Zhf5T9u5uLBWKnVue6XWxprDobftnletJ8+oel7sexFfM3qIxNmVE7LSFGTpv6obNyaQ==}

  '@vue/compiler-ssr@3.5.13':
    resolution: {integrity: sha512-wMH6vrYHxQl/IybKJagqbquvxpWCuVYpoUJfCqFZwa/JY1GdATAQ+TgVtgrwwMZ0D07QhA99rs/EAAWfvG6KpA==}

  '@vue/devtools-api@6.6.4':
    resolution: {integrity: sha512-sGhTPMuXqZ1rVOk32RylztWkfXTRhuS7vgAKv0zjqk8gbsHkJ7xfFf+jbySxt7tWObEJwyKaHMikV/WGDiQm8g==}

  '@vue/devtools-api@7.6.7':
    resolution: {integrity: sha512-PV4I31WaV2rfA8RGauM+69uFEzWkqtP561RiLU2wK+Ce85u3zyKW3aoESlLCNzkc4y0JaJyskH6zAE3xWOP8+Q==}

  '@vue/devtools-core@7.6.7':
    resolution: {integrity: sha512-6fW8Q0H1NHDXdEcuV6dylT5U2Yxg3SdMnVCey99Y6S4R2PNgFL2vC+VU9U9rHIiaoEUkeza42S7FfHxV4VI3Jg==}
    peerDependencies:
      vue: ^3.0.0

  '@vue/devtools-kit@7.6.7':
    resolution: {integrity: sha512-V8/jrXY/swHgnblABG9U4QCbE60c6RuPasmv2d9FvVqc5d94t1vDiESuvRmdNJBdWz4/D3q6ffgyAfRVjwHYEw==}

  '@vue/devtools-shared@7.6.7':
    resolution: {integrity: sha512-QggO6SviAsolrePAXZ/sA1dSicSPt4TueZibCvydfhNDieL1lAuyMTgQDGst7TEvMGb4vgYv2I+1sDkO4jWNnw==}

  '@vue/eslint-config-prettier@10.1.0':
    resolution: {integrity: sha512-J6wV91y2pXc0Phha01k0WOHBTPsoSTf4xlmMjoKaeSxBpAdsgTppGF5RZRdOHM7OA74zAXD+VLANrtYXpiPKkQ==}
    peerDependencies:
      eslint: '>= 8.21.0'
      prettier: '>= 3.0.0'

  '@vue/reactivity@3.5.13':
    resolution: {integrity: sha512-NaCwtw8o48B9I6L1zl2p41OHo/2Z4wqYGGIK1Khu5T7yxrn+ATOixn/Udn2m+6kZKB/J7cuT9DbWWhRxqixACg==}

  '@vue/runtime-core@3.5.13':
    resolution: {integrity: sha512-Fj4YRQ3Az0WTZw1sFe+QDb0aXCerigEpw418pw1HBUKFtnQHWzwojaukAs2X/c9DQz4MQ4bsXTGlcpGxU/RCIw==}

  '@vue/runtime-dom@3.5.13':
    resolution: {integrity: sha512-dLaj94s93NYLqjLiyFzVs9X6dWhTdAlEAciC3Moq7gzAc13VJUdCnjjRurNM6uTLFATRHexHCTu/Xp3eW6yoog==}

  '@vue/server-renderer@3.5.13':
    resolution: {integrity: sha512-wAi4IRJV/2SAW3htkTlB+dHeRmpTiVIK1OGLWV1yeStVSebSQQOwGwIq0D3ZIoBj2C2qpgz5+vX9iEBkTdk5YA==}
    peerDependencies:
      vue: 3.5.13

  '@vue/shared@3.5.13':
    resolution: {integrity: sha512-/hnE/qP5ZoGpol0a5mDi45bOd7t3tjYJBjsgCsivow7D48cJeV5l05RD82lPqi7gRiphZM37rnhW1l6ZoCNNnQ==}

  '@vueuse/core@10.11.1':
    resolution: {integrity: sha512-guoy26JQktXPcz+0n3GukWIy/JDNKti9v6VEMu6kV2sYBsWuGiTU8OWdg+ADfUbHg3/3DlqySDe7JmdHrktiww==}

  '@vueuse/core@11.3.0':
    resolution: {integrity: sha512-7OC4Rl1f9G8IT6rUfi9JrKiXy4bfmHhZ5x2Ceojy0jnd3mHNEvV4JaRygH362ror6/NZ+Nl+n13LPzGiPN8cKA==}

  '@vueuse/integrations@10.11.1':
    resolution: {integrity: sha512-Y5hCGBguN+vuVYTZmdd/IMXLOdfS60zAmDmFYc4BKBcMUPZH1n4tdyDECCPjXm0bNT3ZRUy1xzTLGaUje8Xyaw==}
    peerDependencies:
      async-validator: ^4
      axios: ^1
      change-case: ^4
      drauu: ^0.3
      focus-trap: ^7
      fuse.js: ^6
      idb-keyval: ^6
      jwt-decode: ^3
      nprogress: ^0.2
      qrcode: ^1.5
      sortablejs: ^1
      universal-cookie: ^6
    peerDependenciesMeta:
      async-validator:
        optional: true
      axios:
        optional: true
      change-case:
        optional: true
      drauu:
        optional: true
      focus-trap:
        optional: true
      fuse.js:
        optional: true
      idb-keyval:
        optional: true
      jwt-decode:
        optional: true
      nprogress:
        optional: true
      qrcode:
        optional: true
      sortablejs:
        optional: true
      universal-cookie:
        optional: true

  '@vueuse/metadata@10.11.1':
    resolution: {integrity: sha512-IGa5FXd003Ug1qAZmyE8wF3sJ81xGLSqTqtQ6jaVfkeZ4i5kS2mwQF61yhVqojRnenVew5PldLyRgvdl4YYuSw==}

  '@vueuse/metadata@11.3.0':
    resolution: {integrity: sha512-pwDnDspTqtTo2HwfLw4Rp6yywuuBdYnPYDq+mO38ZYKGebCUQC/nVj/PXSiK9HX5otxLz8Fn7ECPbjiRz2CC3g==}

  '@vueuse/motion@2.2.6':
    resolution: {integrity: sha512-gKFktPtrdypSv44SaW1oBJKLBiP6kE5NcoQ6RsAU3InemESdiAutgQncfPe/rhLSLCtL4jTAhMmFfxoR6gm5LQ==}
    peerDependencies:
      vue: '>=3.0.0'

  '@vueuse/shared@10.11.1':
    resolution: {integrity: sha512-LHpC8711VFZlDaYUXEBbFBCQ7GS3dVU9mjOhhMhXP6txTV4EhYQg/KGnQuvt/sPAtoUKq7VVUnL6mVtFoL42sA==}

  '@vueuse/shared@11.3.0':
    resolution: {integrity: sha512-P8gSSWQeucH5821ek2mn/ciCk+MS/zoRKqdQIM3bHq6p7GXDAJLmnRRKmF5F65sAVJIfzQlwR3aDzwCn10s8hA==}

  acorn-jsx@5.3.2:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.14.0:
    resolution: {integrity: sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  algoliasearch@5.15.0:
    resolution: {integrity: sha512-Yf3Swz1s63hjvBVZ/9f2P1Uu48GjmjCN+Esxb6MAONMGtZB1fRX8/S1AhUTtsuTlcGovbYLxpHgc7wEzstDZBw==}
    engines: {node: '>= 14.0.0'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  axios@1.10.0:
    resolution: {integrity: sha512-/1xYAC4MP/HEG+3duIhFr4ZQXR4sQXOIe+o6sdqzeykGLx6Upp/1p8MHqhINOvGeP7xyNHe7tsiJByc4SSVUxw==}

  b-tween@0.3.3:
    resolution: {integrity: sha512-oEHegcRpA7fAuc9KC4nktucuZn2aS8htymCPcP3qkEGPqiBH+GfqtqoG2l7LxHngg6O0HFM7hOeOYExl1Oz4ZA==}

  b-validate@1.5.3:
    resolution: {integrity: sha512-iCvCkGFskbaYtfQ0a3GmcQCHl/Sv1GufXFGuUQ+FE+WJa7A/espLOuFIn09B944V8/ImPj71T4+rTASxO2PAuA==}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  birpc@0.2.19:
    resolution: {integrity: sha512-5WeXXAvTmitV1RqJFppT5QtUiz2p1mRSYU000Jkft5ZUCLJIk4uQriYNO50HknxKwM6jd8utNc66K1qGIwwWBQ==}

  boolbase@1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==}

  brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}

  brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  browserslist@4.24.2:
    resolution: {integrity: sha512-ZIc+Q62revdMcqC6aChtW4jz3My3klmCO1fEmINZY/8J3EpBg5/A/D0AKmBveUh6pgoeycoMkVMko84tuYS+Gg==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  bundle-name@4.1.0:
    resolution: {integrity: sha512-tjwM5exMg6BGRI+kNmTntNsvdZS1X8BFYS6tnJ2hdH0kVxM6/eVZ2xy+FqStSWvYmtfFMDLIxurorHwDKfDz5Q==}
    engines: {node: '>=18'}

  bundle-require@5.0.0:
    resolution: {integrity: sha512-GuziW3fSSmopcx4KRymQEJVbZUfqlCqcq7dvs6TYwKRZiegK/2buMxQTPs6MGlNv50wms1699qYO54R8XfRX4w==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    peerDependencies:
      esbuild: '>=0.18'

  c12@2.0.1:
    resolution: {integrity: sha512-Z4JgsKXHG37C6PYUtIxCfLJZvo6FyhHJoClwwb9ftUkLpPSkuYqn6Tr+vnaN8hymm0kIbcg6Ey3kv/Q71k5w/A==}
    peerDependencies:
      magicast: ^0.3.5
    peerDependenciesMeta:
      magicast:
        optional: true

  cac@6.7.14:
    resolution: {integrity: sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==}
    engines: {node: '>=8'}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  caniuse-lite@1.0.30001686:
    resolution: {integrity: sha512-Y7deg0Aergpa24M3qLC5xjNklnKnhsmSyR/V89dLZ1n0ucJIFNs7PgR2Yfa/Zf6W79SbBicgtGxZr2juHkEUIA==}

  ccount@2.0.1:
    resolution: {integrity: sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  character-entities-html4@2.1.0:
    resolution: {integrity: sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==}

  character-entities-legacy@3.0.0:
    resolution: {integrity: sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==}

  chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}

  chokidar@4.0.1:
    resolution: {integrity: sha512-n8enUVCED/KVRQlab1hr3MVpcVMvxtZjmEa956u+4YijlmQED223XMSYj2tLuKvr4jcCTzNNMpQDUer72MMmzA==}
    engines: {node: '>= 14.16.0'}

  chownr@2.0.0:
    resolution: {integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==}
    engines: {node: '>=10'}

  citty@0.1.6:
    resolution: {integrity: sha512-tskPPKEs8D2KPafUypv2gxwJP8h/OaJmC82QQGGDQcHvXX43xF2VDACcJVmZ0EuSxkpO9Kc4MlrA3q0+FG58AQ==}

  color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  color-string@1.9.1:
    resolution: {integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==}

  color@3.2.1:
    resolution: {integrity: sha512-aBl7dZI9ENN6fUGC7mWpMTPNHmWUSNan9tuWN6ahh5ZLNk9baLJOnSMlrQkHcrfFgz2/RigjUVAjdx36VcemKA==}

  colorette@2.0.20:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  comma-separated-tokens@2.0.3:
    resolution: {integrity: sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  compatx@0.1.8:
    resolution: {integrity: sha512-jcbsEAR81Bt5s1qOFymBufmCbXCXbk0Ql+K5ouj6gCyx2yHlu6AgmGIi9HxfKixpUDO5bCFJUHQ5uM6ecbTebw==}

  compute-scroll-into-view@1.0.20:
    resolution: {integrity: sha512-UCB0ioiyj8CRjtrvaceBLqqhZCVP+1B8+NWQhmdsm0VXOJtobBCf1dBQmebCCo34qZmUwZfIH2MZLqNHazrfjg==}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  confbox@0.1.8:
    resolution: {integrity: sha512-RMtmw0iFkeR4YV+fUOSucriAQNb9g8zFR52MWCtl+cCZOFRNL6zeB395vPzFhEjjn4fMxXudmELnl/KF/WrK6w==}

  consola@3.2.3:
    resolution: {integrity: sha512-I5qxpzLv+sJhTVEoLYNcTW+bThDCPsit0vLNKShZx6rLtpilNpmmeTPaeqJb9ZE9dV3DGaeby6Vuhrw38WjeyQ==}
    engines: {node: ^14.18.0 || >=16.10.0}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  copy-anything@2.0.6:
    resolution: {integrity: sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==}

  copy-anything@3.0.5:
    resolution: {integrity: sha512-yCEafptTtb4bk7GLEQoM8KVJpxAfdBJYaXyzQEgQQQgYrZiDp8SJmGKlYza6CYjEDNstAdNdKA3UuoULlEbS6w==}
    engines: {node: '>=12.13'}

  crelt@1.0.6:
    resolution: {integrity: sha512-VQ2MBenTq1fWZUH9DJNGti7kKv6EeAuYr3cLwxUWhIu1baTaXh4Ib5W2CqHVqib4/MqbYGJqiL3Zb8GJZr3l4g==}

  cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}

  css-tree@3.0.1:
    resolution: {integrity: sha512-8Fxxv+tGhORlshCdCwnNJytvlvq46sOLSYEx2ZIGurahWvMucSRnyjPA3AmrMq4VPRYbHVpWj5VkiVasrM2H4Q==}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0}

  cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  dayjs@1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==}

  debug@4.3.7:
    resolution: {integrity: sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  default-browser-id@5.0.0:
    resolution: {integrity: sha512-A6p/pu/6fyBcA1TRz/GqWYPViplrftcW2gZC9q79ngNCKAeR/X3gcEdXQHl4KNXV+3wgIJ1CPkJQ3IHM6lcsyA==}
    engines: {node: '>=18'}

  default-browser@5.2.1:
    resolution: {integrity: sha512-WY/3TUME0x3KPYdRRxEJJvXRHV4PyPoUsxtZa78lwItwRQRHhd2U9xOscaT/YTf8uCXIAjeJOFBVEh/7FtD8Xg==}
    engines: {node: '>=18'}

  define-lazy-prop@3.0.0:
    resolution: {integrity: sha512-N+MeXYoqr3pOgn8xfyRPREN7gHakLYjhsHhWGT3fWAiL4IkAt0iDw14QiiEm2bE30c5XX5q0FtAA3CK5f9/BUg==}
    engines: {node: '>=12'}

  defu@6.1.4:
    resolution: {integrity: sha512-mEQCMmwJu317oSz8CwdIOdwf3xMif1ttiM8LTufzc3g6kR+9Pe236twL8j3IYT1F7GfRgGcW6MWxzZjLIkuHIg==}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  dequal@2.0.3:
    resolution: {integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==}
    engines: {node: '>=6'}

  destr@2.0.3:
    resolution: {integrity: sha512-2N3BOUU4gYMpTP24s5rF5iP7BDr7uNTCs4ozw3kf/eKfvWSIu93GEBi5m427YoyJoeOzQ5smuu4nNAPGb8idSQ==}

  detect-libc@1.0.3:
    resolution: {integrity: sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==}
    engines: {node: '>=0.10'}
    hasBin: true

  devlop@1.1.0:
    resolution: {integrity: sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==}

  dotenv@16.4.7:
    resolution: {integrity: sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==}
    engines: {node: '>=12'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  duplexer@0.1.2:
    resolution: {integrity: sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==}

  electron-to-chromium@1.5.71:
    resolution: {integrity: sha512-dB68l59BI75W1BUGVTAEJy45CEVuEGy9qPVVQ8pnHyHMn36PLPPoE1mjLH+lo9rKulO3HC2OhbACI/8tCqJBcA==}

  emoji-regex-xs@1.0.0:
    resolution: {integrity: sha512-LRlerrMYoIDrT6jgpeZ2YYl/L8EulRTt5hQcYjy5AInh7HWXKimpqx68aknBFpGL2+/IcogTcaydJEgaTmOpDg==}

  entities@3.0.1:
    resolution: {integrity: sha512-WiyBqoomrwMdFG1e0kqvASYfnlb0lp8M5o5Fw2OFq1hNZxxcNk8Ik0Xm7LxzBhuidnZB/UtBqVCgUz3kBOP51Q==}
    engines: {node: '>=0.12'}

  entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}

  errno@0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==}
    hasBin: true

  error-stack-parser-es@0.1.5:
    resolution: {integrity: sha512-xHku1X40RO+fO8yJ8Wh2f2rZWVjqyhb1zgq1yZ8aZRQkv6OOKhKWRUaht3eSCUbAOBaKIgM+ykwFLE+QUxgGeg==}

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-drager@1.3.0:
    resolution: {integrity: sha512-0ZARhTQvMijArODhz2We8+8e39QyxBJEe5X+qFjlecQtbZd1KAF7jJ8EmBnEOfDF9Cnaw+WS0WHMOWkhOxO4ng==}
    peerDependencies:
      vue: '>=3.2.0'

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==}
    engines: {node: '>= 0.4'}

  esbuild@0.21.5:
    resolution: {integrity: sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==}
    engines: {node: '>=12'}
    hasBin: true

  esbuild@0.23.1:
    resolution: {integrity: sha512-VVNz/9Sa0bs5SELtn3f7qhJCDPCF5oMEl5cO9/SSinpE9hbPVvxbd572HH5AKiP7WD8INO53GgfDDhRjkylHEg==}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}

  escape-string-regexp@5.0.0:
    resolution: {integrity: sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==}
    engines: {node: '>=12'}

  eslint-config-prettier@9.1.0:
    resolution: {integrity: sha512-NSWl5BFQWEPi1j4TjVNItzYV7dZXZ+wP6I6ZhrBGpChQhZRUaElihE9uRRkcbRnNb76UMKDF3r+WTmNcGPKsqw==}
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-plugin-prettier@5.2.1:
    resolution: {integrity: sha512-gH3iR3g4JfF+yYPaJYkN7jEl9QbweL/YfkoRlNnuIEHEz1vHVlCmWOS+eGGiRuzHQXdJFCOTxRgvju9b8VUmrw==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      '@types/eslint': '>=8.0.0'
      eslint: '>=8.0.0'
      eslint-config-prettier: '*'
      prettier: '>=3.0.0'
    peerDependenciesMeta:
      '@types/eslint':
        optional: true
      eslint-config-prettier:
        optional: true

  eslint-plugin-vue@9.32.0:
    resolution: {integrity: sha512-b/Y05HYmnB/32wqVcjxjHZzNpwxj1onBOvqW89W+V+XNG1dRuaFbNd3vT9CLbr2LXjEoq+3vn8DanWf7XU22Ug==}
    engines: {node: ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.2.0 || ^7.0.0 || ^8.0.0 || ^9.0.0

  eslint-scope@7.2.2:
    resolution: {integrity: sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-scope@8.2.0:
    resolution: {integrity: sha512-PHlWUfG6lvPc3yvP5A4PNyBL1W8fkDUccmI21JUu/+GKZBoH/W5u6usENXUrWFRsyoW5ACUjFGgAFQp5gUlb/A==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@4.2.0:
    resolution: {integrity: sha512-UyLnSehNt62FFhSwjZlHmeokpRK59rcz29j+F1/aDgbkbRTk7wIc9XzdoasMUbRNKDM0qQt/+BJ4BrpFeABemw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint@9.16.0:
    resolution: {integrity: sha512-whp8mSQI4C8VXd+fLgSM0lh3UlmcFtVwUQjyKCFfsp+2ItAIYhlq/hqGahGqHE6cv9unM41VlqKk2VtKYR2TaA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true

  espree@10.3.0:
    resolution: {integrity: sha512-0QYC8b24HWY8zjRnDTL6RiHfDbAWn63qb4LMj1Z4b076A4une81+z03Kg7l7mn/48PUTqoLptSXez8oknU8Clg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  espree@9.6.1:
    resolution: {integrity: sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  esquery@1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}

  estree-walker@3.0.3:
    resolution: {integrity: sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  execa@8.0.1:
    resolution: {integrity: sha512-VyhnebXciFV2DESc+p6B+y0LjSm0krU4OgJN44qFAhBY0TJ+1V61tYD2+wHusZ6F9n5K+vl8k0sTy7PEfV4qpg==}
    engines: {node: '>=16.17'}

  execa@9.5.1:
    resolution: {integrity: sha512-QY5PPtSonnGwhhHDNI7+3RvY285c7iuJFFB+lU+oEzMY/gEGJ808owqJsrr8Otd1E/x07po1LkUBmdAc5duPAg==}
    engines: {node: ^18.19.0 || >=20.5.0}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-diff@1.3.0:
    resolution: {integrity: sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==}

  fast-glob@3.3.2:
    resolution: {integrity: sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  fastq@1.17.1:
    resolution: {integrity: sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==}

  fdir@6.4.2:
    resolution: {integrity: sha512-KnhMXsKSPZlAhp7+IjUkRZKPb4fUyccpDrdFXbi4QL1qkmFh9kVY09Yox+n4MaOb3lHZ1Tv829C3oaaXoMYPDQ==}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  figures@6.1.0:
    resolution: {integrity: sha512-d+l3qxjSesT4V7v2fh+QnmFnUWv9lSpjarhShNTgBOfA0ttejbQUAlHLitbjkoRiDulW0OPoQPYIGhIC8ohejg==}
    engines: {node: '>=18'}

  file-entry-cache@8.0.0:
    resolution: {integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==}
    engines: {node: '>=16.0.0'}

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}

  flat-cache@4.0.1:
    resolution: {integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==}
    engines: {node: '>=16'}

  flatted@3.3.2:
    resolution: {integrity: sha512-AiwGJM8YcNOaobumgtng+6NHuOqC3A7MixFeDafM3X9cIUM+xUXoS5Vfgf+OihAYe20fxqNM9yPBXJzRtZ/4eA==}

  focus-trap@7.6.2:
    resolution: {integrity: sha512-9FhUxK1hVju2+AiQIDJ5Dd//9R2n2RAfJ0qfhF4IHGHgcoEUTMpbTeG/zbEuwaiYXfuAH6XE0/aCyxDdRM+W5w==}

  follow-redirects@1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  form-data@4.0.3:
    resolution: {integrity: sha512-qsITQPfmvMOSAdeyZ+12I1c+CKSstAFAwu+97zrnWAbIr5u8wfsExUzCesVLC8NgHuRUqNN4Zy6UPWUTRGslcA==}
    engines: {node: '>= 6'}

  framesync@6.1.2:
    resolution: {integrity: sha512-jBTqhX6KaQVDyus8muwZbBeGGP0XgujBRbQ7gM7BRdS3CadCZIHiawyzYLnafYcvZIh5j8WE7cxZKFn7dXhu9g==}

  fs-extra@11.2.0:
    resolution: {integrity: sha512-PmDi3uwK5nFuXh7XDTlVnS17xJS7vW36is2+w3xcv8SVxiB4NyATf4ctkVY5bkSjX0Y4nbvZCq1/EjtEyr9ktw==}
    engines: {node: '>=14.14'}

  fs-minipass@2.1.0:
    resolution: {integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==}
    engines: {node: '>= 8'}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  get-stream@8.0.1:
    resolution: {integrity: sha512-VaUJspBffn/LMCJVoMvSAdmscJyS1auj5Zulnn5UoYcY531UWmdwhRWkcGKnGU93m5HSXP9LP2usOryrBtQowA==}
    engines: {node: '>=16'}

  get-stream@9.0.1:
    resolution: {integrity: sha512-kVCxPF3vQM/N0B1PmoqVUqgHP+EeVjmZSQn+1oCRPxd2P21P2F19lIgbR3HBosbB1PUhOAoctJnfEn2GbN2eZA==}
    engines: {node: '>=18'}

  get-tsconfig@4.8.1:
    resolution: {integrity: sha512-k9PN+cFBmaLWtVz29SkUoqU5O0slLuHJXt/2P+tMVFT+phsSGXGkp9t3rQIqdz0e+06EHNGs3oM6ZX1s2zHxRg==}

  giget@1.2.3:
    resolution: {integrity: sha512-8EHPljDvs7qKykr6uw8b+lqLiUc/vUg+KVTI0uND4s63TdsZM2Xus3mflvF0DDG9SiM4RlCkFGL+7aAjRmV7KA==}
    hasBin: true

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  globals@13.24.0:
    resolution: {integrity: sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==}
    engines: {node: '>=8'}

  globals@14.0.0:
    resolution: {integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==}
    engines: {node: '>=18'}

  globby@14.0.2:
    resolution: {integrity: sha512-s3Fq41ZVh7vbbe2PN3nrW7yC7U7MFVc5c98/iTl9c2GawNMKx/J648KQRW6WKkuU8GIbbh2IXfIRQjOZnXcTnw==}
    engines: {node: '>=18'}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  gzip-size@6.0.0:
    resolution: {integrity: sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q==}
    engines: {node: '>=10'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}

  hash-sum@2.0.0:
    resolution: {integrity: sha512-WdZTbAByD+pHfl/g9QSsBIIwy8IT+EsPiKDs0KNX+zSHhdDLFKdZu0BQHljvO+0QI/BasbMSUa8wYNCZTvhslg==}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  hast-util-to-html@9.0.3:
    resolution: {integrity: sha512-M17uBDzMJ9RPCqLMO92gNNUDuBSq10a25SDBI08iCCxmorf4Yy6sYHK57n9WAbRAAaU+DuR4W6GN9K4DFZesYg==}

  hast-util-whitespace@3.0.0:
    resolution: {integrity: sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw==}

  hey-listen@1.0.8:
    resolution: {integrity: sha512-COpmrF2NOg4TBWUJ5UVyaCU2A88wEMkUPK4hNqyCkqHbxT92BbvfjoSozkAIIm6XhicGlJHhFdullInrdhwU8Q==}

  hookable@5.5.3:
    resolution: {integrity: sha512-Yc+BQe8SvoXH1643Qez1zqLRmbA5rCL+sSmk6TVos0LWVfNIB7PGncdlId77WzLGSIB5KaWgTaNTs2lNVEI6VQ==}

  html-tags@3.3.1:
    resolution: {integrity: sha512-ztqyC3kLto0e9WbNp0aeP+M3kTt+nbaIveGmUxAtZa+8iFgKLUOD4YKM5j+f3QD89bra7UeumolZHKuOXnTmeQ==}
    engines: {node: '>=8'}

  html-void-elements@3.0.0:
    resolution: {integrity: sha512-bEqo66MRXsUGxWHV5IP0PUiAWwoEjba4VCzg0LjFJBpchPaTfyfCKTG6bc5F8ucKec3q5y6qOdGyYTSBEvhCrg==}

  human-signals@5.0.0:
    resolution: {integrity: sha512-AXcZb6vzzrFAUE61HnN4mpLqd/cSIwNQjtNWR0euPm6y0iqx3G4gOXaIDdtdDwZmhwe82LA6+zinmW4UBWVePQ==}
    engines: {node: '>=16.17.0'}

  human-signals@8.0.0:
    resolution: {integrity: sha512-/1/GPCpDUCCYwlERiYjxoczfP0zfvZMU/OWgQPMya9AbAE24vseigFdhAMObpc8Q4lc/kjutPfUddDYyAmejnA==}
    engines: {node: '>=18.18.0'}

  i18next@23.16.8:
    resolution: {integrity: sha512-06r/TitrM88Mg5FdUXAKL96dJMzgqLE5dv3ryBAra4KCwD9mJ4ndOTS95ZuymIGoE+2hzfdaMak2X11/es7ZWg==}

  iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}

  ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}

  ignore@6.0.2:
    resolution: {integrity: sha512-InwqeHHN2XpumIkMvpl/DCJVrAHgCsG5+cn1XlnLWGwtZBm8QJfSusItfrwx81CTp5agNZqpKU2J/ccC5nGT4A==}
    engines: {node: '>= 4'}

  image-size@0.5.5:
    resolution: {integrity: sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  immutable@5.0.3:
    resolution: {integrity: sha512-P8IdPQHq3lA1xVeBRi5VPqUm5HDgKnx0Ru51wZz5mjxHr5n3RWhjIpOFU7ybkUxfB+5IToy+OLaHYDBIWsv+uw==}

  import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}

  importx@0.4.4:
    resolution: {integrity: sha512-Lo1pukzAREqrBnnHC+tj+lreMTAvyxtkKsMxLY8H15M/bvLl54p3YuoTI70Tz7Il0AsgSlD7Lrk/FaApRcBL7w==}

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  is-arrayish@0.3.2:
    resolution: {integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-docker@3.0.0:
    resolution: {integrity: sha512-eljcgEDlEns/7AXFosB5K/2nCM4P7FQPkGc/DWLy5rmFEWvZayGrik1d9/QIY5nJ4f9YsVvBkA6kJpHn9rISdQ==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    hasBin: true

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-inside-container@1.0.0:
    resolution: {integrity: sha512-KIYLCCJghfHZxqjYBE7rEy0OBuTd5xCHS7tHVgvCLkx7StIoaxwNW3hCALgEUjFfeRk+MG/Qxmp/vtETEF3tRA==}
    engines: {node: '>=14.16'}
    hasBin: true

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-plain-obj@4.1.0:
    resolution: {integrity: sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==}
    engines: {node: '>=12'}

  is-stream@3.0.0:
    resolution: {integrity: sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  is-stream@4.0.1:
    resolution: {integrity: sha512-Dnz92NInDqYckGEUJv689RbRiTSEHCQ7wOVeALbkOz999YpqT46yMRIGtSNl2iCL1waAZSx40+h59NV/EwzV/A==}
    engines: {node: '>=18'}

  is-unicode-supported@2.1.0:
    resolution: {integrity: sha512-mE00Gnza5EEB3Ds0HfMyllZzbBrmLOX3vfWoj9A9PEnTfratQ/BcaJOuMhnkhjXvb2+FkY3VuHqtAGpTPmglFQ==}
    engines: {node: '>=18'}

  is-what@3.14.1:
    resolution: {integrity: sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==}

  is-what@4.1.16:
    resolution: {integrity: sha512-ZhMwEosbFJkA0YhFnNDgTM4ZxDRsS6HqTo7qsZM08fehyRYIYa0yHu5R6mgo1n/8MgaPBXiPimPD77baVFYg+A==}
    engines: {node: '>=12.13'}

  is-wsl@3.1.0:
    resolution: {integrity: sha512-UcVfVfaK4Sc4m7X3dUSoHoozQGBEFeDC+zVo06t98xe8CzHSZZBekNXH+tu0NalHolcJ/QAGqS46Hef7QXBIMw==}
    engines: {node: '>=16'}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  isomorphic.js@0.2.5:
    resolution: {integrity: sha512-PIeMbHqMt4DnUP3MA/Flc0HElYjMXArsw1qwJZcm9sqR8mq3l8NYizFMty0pWwE/tzIGH3EKK5+jes5mAr85yw==}

  jiti@1.21.6:
    resolution: {integrity: sha512-2yTgeWTWzMWkHu6Jp9NKgePDaYHbntiwvYuuJLbbN9vl7DC9DvXKOB2BC3ZZ92D3cvV/aflH0osDfwpHepQ53w==}
    hasBin: true

  jiti@2.0.0-beta.3:
    resolution: {integrity: sha512-pmfRbVRs/7khFrSAYnSiJ8C0D5GvzkE4Ey2pAvUcJsw1ly/p+7ut27jbJrjY79BpAJQJ4gXYFtK6d1Aub+9baQ==}
    hasBin: true

  jiti@2.4.1:
    resolution: {integrity: sha512-yPBThwecp1wS9DmoA4x4KR2h3QoslacnDR8ypuFM962kI4/456Iy1oHx2RAgh4jfZNdn0bctsdadceiBUgpU1g==}
    hasBin: true

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-tokens@9.0.1:
    resolution: {integrity: sha512-mxa9E9ITFOt0ban3j6L5MpjwegGz6lBQmM1IJkWeBZGcMxto50+eWdjC/52xDbS2vy0k7vIMK0Fe2wfL9OQSpQ==}

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsesc@3.0.2:
    resolution: {integrity: sha512-xKqzzWXDttJuOcawBt4KnKHHIf5oQ/Cxax+0PWFG+DFDgHNAdi+TXECADI+RYiFUMmx8792xsMbbgXj4CwnP4g==}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}

  keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}

  klona@2.0.6:
    resolution: {integrity: sha512-dhG34DXATL5hSxJbIexCft8FChFXtmskoZYnoPWjXQuebWYCNkVeV3KkGegCK9CP1oswI/vQibS2GY7Em/sJJA==}
    engines: {node: '>= 8'}

  knitwork@1.1.0:
    resolution: {integrity: sha512-oHnmiBUVHz1V+URE77PNot2lv3QiYU2zQf1JjOVkMt3YDKGbu8NAFr+c4mcNOhdsGrB/VpVbRwPwhiXrPhxQbw==}

  kolorist@1.8.0:
    resolution: {integrity: sha512-Y+60/zizpJ3HRH8DCss+q95yr6145JXZo46OTpFvDZWLfRCE4qChOyk1b26nMaNpfHHgxagk9dXT5OP0Tfe+dQ==}

  less@4.4.0:
    resolution: {integrity: sha512-kdTwsyRuncDfjEs0DlRILWNvxhDG/Zij4YLO4TMJgDLW+8OzpfkdPnRgrsRuY1o+oaxJGWsps5f/RVBgGmmN0w==}
    engines: {node: '>=14'}
    hasBin: true

  levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}

  lib0@0.2.109:
    resolution: {integrity: sha512-jP0gbnyW0kwlx1Atc4dcHkBbrVAkdHjuyHxtClUPYla7qCmwIif1qZ6vQeJdR5FrOVdn26HvQT0ko01rgW7/Xw==}
    engines: {node: '>=16'}
    hasBin: true

  linkify-it@4.0.1:
    resolution: {integrity: sha512-C7bfi1UZmoj8+PQx22XyeXCuBlokoyWQL5pWSP+EI6nzRylyThouddufc2c1NDIcP9k5agmN9fLpA7VNJfIiqw==}

  linkify-it@5.0.0:
    resolution: {integrity: sha512-5aHCbzQRADcdP+ATqnDuhhJ/MRIqDkZX5pyjFHRRysS8vZ5AbqGEoFIb6pYHPZ+L/OC2Lc+xT8uHVVR5CAK/wQ==}

  linkifyjs@4.2.0:
    resolution: {integrity: sha512-pCj3PrQyATaoTYKHrgWRF3SJwsm61udVh+vuls/Rl6SptiDhgE7ziUIudAedRY9QEfynmM7/RmLEfPUyw1HPCw==}

  load-tsconfig@0.2.5:
    resolution: {integrity: sha512-IXO6OCs9yg8tMKzfPZ1YmheJbZCiEsnBdcB03l0OcfK9prKnJb96siuHCr5Fl37/yo9DnKU+TLpxzTUspw9shg==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  local-pkg@0.5.1:
    resolution: {integrity: sha512-9rrA30MRRP3gBD3HTGnC6cDFpaE1kVDWxWgqWJUN0RvDNAo+Nz/9GxB+nHOH0ifbVFy0hSA1V6vFDvnx54lTEQ==}
    engines: {node: '>=14'}

  locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  magic-string@0.30.14:
    resolution: {integrity: sha512-5c99P1WKTed11ZC0HMJOj6CDIue6F8ySu+bJL+85q1zBEIY8IklrJ1eiKC2NDRh3Ct3FcvmJPyQHb9erXMTJNw==}

  make-dir@2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==}
    engines: {node: '>=6'}

  mark.js@8.11.1:
    resolution: {integrity: sha512-1I+1qpDt4idfgLQG+BNWmrqku+7/2bi5nLf4YwF8y8zXvmfiTBY3PV3ZibfrjBueCByROpuBjLLFCajqkgYoLQ==}

  markdown-it-container@3.0.0:
    resolution: {integrity: sha512-y6oKTq4BB9OQuY/KLfk/O3ysFhB3IMYoIWhGJEidXt1NQFocFK2sA2t0NYZAMyMShAGL6x5OPIbrmXPIqaN9rw==}

  markdown-it@13.0.2:
    resolution: {integrity: sha512-FtwnEuuK+2yVU7goGn/MJ0WBZMM9ZPgU9spqlFs7/A/pDIUNSOQZhUgOqYCficIuR2QaFnrt8LHqBWsbTAoI5w==}
    hasBin: true

  markdown-it@14.1.0:
    resolution: {integrity: sha512-a54IwgWPaeBCAAsv13YgmALOF1elABB08FxO9i+r4VFk5Vl4pKokRPeX8u5TCgSsPi6ec1otfLjdOpVcgbpshg==}
    hasBin: true

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  mdast-util-to-hast@13.2.0:
    resolution: {integrity: sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA==}

  mdn-data@2.12.1:
    resolution: {integrity: sha512-rsfnCbOHjqrhWxwt5/wtSLzpoKTzW7OXdT5lLOIH1OTYhWu9rRJveGq0sKvDZODABH7RX+uoR+DYcpFnq4Tf6Q==}

  mdurl@1.0.1:
    resolution: {integrity: sha512-/sKlQJCBYVY9Ers9hqzKou4H6V5UWc/M59TH2dvkt+84itfnq7uFOMLpOiOS4ujvHP4etln18fmIxA5R5fll0g==}

  mdurl@2.0.0:
    resolution: {integrity: sha512-Lf+9+2r+Tdp5wXDXC4PcIBjTDtq4UKjCPMQhKIuzpJNW0b96kVqSwW0bT7FhRSfmAiFYgP+SCRvdrDozfh0U5w==}

  merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  micromark-util-character@2.1.1:
    resolution: {integrity: sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==}

  micromark-util-encode@2.0.1:
    resolution: {integrity: sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw==}

  micromark-util-sanitize-uri@2.0.1:
    resolution: {integrity: sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==}

  micromark-util-symbol@2.0.1:
    resolution: {integrity: sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q==}

  micromark-util-types@2.0.1:
    resolution: {integrity: sha512-534m2WhVTddrcKVepwmVEVnUAmtrx9bfIjNoQHRqfnvdaHQiFytEhJoTgpWJvDEXCO5gLTQh3wYC1PgOJA4NSQ==}

  micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  mimic-fn@4.0.0:
    resolution: {integrity: sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==}
    engines: {node: '>=12'}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}

  minipass@3.3.6:
    resolution: {integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==}
    engines: {node: '>=8'}

  minipass@5.0.0:
    resolution: {integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==}
    engines: {node: '>=8'}

  minisearch@6.3.0:
    resolution: {integrity: sha512-ihFnidEeU8iXzcVHy74dhkxh/dn8Dc08ERl0xwoMMGqp4+LvRSCgicb+zGqWthVokQKvCSxITlh3P08OzdTYCQ==}

  minizlib@2.1.2:
    resolution: {integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==}
    engines: {node: '>= 8'}

  mitt@3.0.1:
    resolution: {integrity: sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw==}

  mkdirp@1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==}
    engines: {node: '>=10'}
    hasBin: true

  mlly@1.7.3:
    resolution: {integrity: sha512-xUsx5n/mN0uQf4V548PKQ+YShA4/IW0KI1dZhrNrPCLG+xizETbHTkOa1f8/xut9JRPp8kQuMnz0oqwkTiLo/A==}

  mri@1.2.0:
    resolution: {integrity: sha512-tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA==}
    engines: {node: '>=4'}

  mrmime@2.0.0:
    resolution: {integrity: sha512-eu38+hdgojoyq63s+yTpN4XMBdt5l8HhMhc4VKLO9KM5caLIBvUm4thi7fFaxyTmCKeNnXZ5pAlBwCUnhA09uw==}
    engines: {node: '>=10'}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  nanoid@3.3.8:
    resolution: {integrity: sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  nanoid@5.0.9:
    resolution: {integrity: sha512-Aooyr6MXU6HpvvWXKoVoXwKMs/KyVakWwg7xQfv5/S/RIgJMy0Ifa45H9qqYy7pTCszrHzP21Uk4PZq2HpEM8Q==}
    engines: {node: ^18 || >=20}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  needle@3.3.1:
    resolution: {integrity: sha512-6k0YULvhpw+RoLNiQCRKOl09Rv1dPLr8hHnVjHqdolKwDrdNyk+Hmrthi4lIGPPz3r39dLx0hsF5s40sZ3Us4Q==}
    engines: {node: '>= 4.4.x'}
    hasBin: true

  node-addon-api@7.1.1:
    resolution: {integrity: sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==}

  node-fetch-native@1.6.4:
    resolution: {integrity: sha512-IhOigYzAKHd244OC0JIMIUrjzctirCmPkaIfhDeGcEETWof5zKYUW7e7MYvChGWh/4CJeXEgsRyGzuF334rOOQ==}

  node-releases@2.0.18:
    resolution: {integrity: sha512-d9VeXT4SJ7ZeOqGX6R5EM022wpL+eWPooLI+5UpWn2jCT1aosUQEhQP214x33Wkwx3JQMvIm+tIoVOdodFS40g==}

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  npm-run-path@5.3.0:
    resolution: {integrity: sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  npm-run-path@6.0.0:
    resolution: {integrity: sha512-9qny7Z9DsQU8Ou39ERsPU4OZQlSTP47ShQzuKZ6PRXpYLtIFgl/DEBYEXKlvcEa+9tHVcK8CF81Y2V72qaZhWA==}
    engines: {node: '>=18'}

  nth-check@2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}

  number-precision@1.6.0:
    resolution: {integrity: sha512-05OLPgbgmnixJw+VvEh18yNPUo3iyp4BEWJcrLu4X9W05KmMifN7Mu5exYvQXqxxeNWhvIF+j3Rij+HmddM/hQ==}

  nypm@0.3.12:
    resolution: {integrity: sha512-D3pzNDWIvgA+7IORhD/IuWzEk4uXv6GsgOxiid4UU3h9oq5IqV1KtPDi63n4sZJ/xcWlr88c0QM2RgN5VbOhFA==}
    engines: {node: ^14.16.0 || >=16.10.0}
    hasBin: true

  ofetch@1.4.1:
    resolution: {integrity: sha512-QZj2DfGplQAr2oj9KzceK9Hwz6Whxazmn85yYeVuS3u9XTMOGMRx0kO95MQ+vLsj/S/NwBDMMLU5hpxvI6Tklw==}

  ohash@1.1.4:
    resolution: {integrity: sha512-FlDryZAahJmEF3VR3w1KogSEdWX3WhA5GPakFx4J81kEAiHyLMpdLLElS8n8dfNadMgAne/MywcvmogzscVt4g==}

  onetime@6.0.0:
    resolution: {integrity: sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==}
    engines: {node: '>=12'}

  oniguruma-to-es@0.7.0:
    resolution: {integrity: sha512-HRaRh09cE0gRS3+wi2zxekB+I5L8C/gN60S+vb11eADHUaB/q4u8wGGOX3GvwvitG8ixaeycZfeoyruKQzUgNg==}

  open@10.1.0:
    resolution: {integrity: sha512-mnkeQ1qP5Ue2wd+aivTD3NHd/lZ96Lu0jgf0pwktLPtx6cTZiH7tyeGRRHs0zX0rbrahXPnXlUnbeXyaBBuIaw==}
    engines: {node: '>=18'}

  optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}

  orderedmap@2.1.1:
    resolution: {integrity: sha512-TvAWxi0nDe1j/rtMcWcIj94+Ffe6n7zhow33h40SKxmsmozs6dz/e+EajymfoFcHd7sxNn8yHM8839uixMOV6g==}

  p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}

  p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}

  package-manager-detector@0.2.7:
    resolution: {integrity: sha512-g4+387DXDKlZzHkP+9FLt8yKj8+/3tOkPv7DVTJGGRm00RkEWgqbFstX1mXJ4M0VDYhUqsTOiISqNOJnhAu3PQ==}

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parse-ms@4.0.0:
    resolution: {integrity: sha512-TXfryirbmq34y8QBwgqCVLi+8oA3oWx2eAnSn62ITyEhEYaWRlVZ2DvMM9eZbMs/RfxPu/PK/aBLyGj4IrqMHw==}
    engines: {node: '>=18'}

  parse-node-version@1.0.1:
    resolution: {integrity: sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==}
    engines: {node: '>= 0.10'}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-key@4.0.0:
    resolution: {integrity: sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==}
    engines: {node: '>=12'}

  path-type@5.0.0:
    resolution: {integrity: sha512-5HviZNaZcfqP95rwpv+1HDgUamezbqdSYTyzjTvwtJSnIH+3vnbmWsItli8OFEndS984VT55M3jduxZbX351gg==}
    engines: {node: '>=12'}

  pathe@1.1.2:
    resolution: {integrity: sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==}

  perfect-debounce@1.0.0:
    resolution: {integrity: sha512-xCy9V055GLEqoFaHoC1SoLIaLmWctgCUaBaWxDZ7/Zx4CTyX7cJQLJOok/orfjZAh9kEYpjJa4d0KcJmCbctZA==}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  picomatch@4.0.2:
    resolution: {integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==}
    engines: {node: '>=12'}

  pify@4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}

  pinia@2.3.0:
    resolution: {integrity: sha512-ohZj3jla0LL0OH5PlLTDMzqKiVw2XARmC1XYLdLWIPBMdhDW/123ZWr4zVAhtJm+aoSkFa13pYXskAvAscIkhQ==}
    peerDependencies:
      typescript: '>=4.4.4'
      vue: ^2.7.0 || ^3.5.11
    peerDependenciesMeta:
      typescript:
        optional: true

  pkg-types@1.2.1:
    resolution: {integrity: sha512-sQoqa8alT3nHjGuTjuKgOnvjo4cljkufdtLMnO2LBP/wRwuDlo1tkaEdMxCRhyGRPacv/ztlZgDPm2b7FAmEvw==}

  popmotion@11.0.5:
    resolution: {integrity: sha512-la8gPM1WYeFznb/JqF4GiTkRRPZsfaj2+kCxqQgr2MJylMmIKUwBfWW8Wa5fml/8gmtlD5yI01MP1QCZPWmppA==}

  postcss-selector-parser@6.1.2:
    resolution: {integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==}
    engines: {node: '>=4'}

  postcss@8.4.49:
    resolution: {integrity: sha512-OCVPnIObs4N29kxTjzLfUryOkvZEq+pf8jTF0lg8E7uETuWHA+v7j3c/xJmiqpX450191LlmZfUKkXxkTry7nA==}
    engines: {node: ^10 || ^12 || >=14}

  preact@10.25.1:
    resolution: {integrity: sha512-frxeZV2vhQSohQwJ7FvlqC40ze89+8friponWUFeVEkaCfhC6Eu4V0iND5C9CXz8JLndV07QRDeXzH1+Anz5Og==}

  prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}

  prettier-linter-helpers@1.0.0:
    resolution: {integrity: sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==}
    engines: {node: '>=6.0.0'}

  prettier@3.4.2:
    resolution: {integrity: sha512-e9MewbtFo+Fevyuxn/4rrcDAaq0IYxPGLvObpQjiZBMAzB9IGmzlnG9RZy3FFas+eBMu2vA0CszMeduow5dIuQ==}
    engines: {node: '>=14'}
    hasBin: true

  pretty-ms@9.2.0:
    resolution: {integrity: sha512-4yf0QO/sllf/1zbZWYnvWw3NxCQwLXKzIj0G849LSufP15BXKM0rbD2Z3wVnkMfjdn/CB0Dpp444gYAACdsplg==}
    engines: {node: '>=18'}

  property-information@6.5.0:
    resolution: {integrity: sha512-PgTgs/BlvHxOu8QuEN7wi5A0OmXaBcHpmCSTehcs6Uuu9IkDIEo13Hy7n898RHfrQ49vKCoGeWZSaAK01nwVig==}

  prosemirror-changeset@2.2.1:
    resolution: {integrity: sha512-J7msc6wbxB4ekDFj+n9gTW/jav/p53kdlivvuppHsrZXCaQdVgRghoZbSS3kwrRyAstRVQ4/+u5k7YfLgkkQvQ==}

  prosemirror-collab@1.3.1:
    resolution: {integrity: sha512-4SnynYR9TTYaQVXd/ieUvsVV4PDMBzrq2xPUWutHivDuOshZXqQ5rGbZM84HEaXKbLdItse7weMGOUdDVcLKEQ==}

  prosemirror-commands@1.6.2:
    resolution: {integrity: sha512-0nDHH++qcf/BuPLYvmqZTUUsPJUCPBUXt0J1ErTcDIS369CTp773itzLGIgIXG4LJXOlwYCr44+Mh4ii6MP1QA==}

  prosemirror-dropcursor@1.8.1:
    resolution: {integrity: sha512-M30WJdJZLyXHi3N8vxN6Zh5O8ZBbQCz0gURTfPmTIBNQ5pxrdU7A58QkNqfa98YEjSAL1HUyyU34f6Pm5xBSGw==}

  prosemirror-gapcursor@1.3.2:
    resolution: {integrity: sha512-wtjswVBd2vaQRrnYZaBCbyDqr232Ed4p2QPtRIUK5FuqHYKGWkEwl08oQM4Tw7DOR0FsasARV5uJFvMZWxdNxQ==}

  prosemirror-history@1.4.1:
    resolution: {integrity: sha512-2JZD8z2JviJrboD9cPuX/Sv/1ChFng+xh2tChQ2X4bB2HeK+rra/bmJ3xGntCcjhOqIzSDG6Id7e8RJ9QPXLEQ==}

  prosemirror-inputrules@1.4.0:
    resolution: {integrity: sha512-6ygpPRuTJ2lcOXs9JkefieMst63wVJBgHZGl5QOytN7oSZs3Co/BYbc3Yx9zm9H37Bxw8kVzCnDsihsVsL4yEg==}

  prosemirror-keymap@1.2.2:
    resolution: {integrity: sha512-EAlXoksqC6Vbocqc0GtzCruZEzYgrn+iiGnNjsJsH4mrnIGex4qbLdWWNza3AW5W36ZRrlBID0eM6bdKH4OStQ==}

  prosemirror-markdown@1.13.1:
    resolution: {integrity: sha512-Sl+oMfMtAjWtlcZoj/5L/Q39MpEnVZ840Xo330WJWUvgyhNmLBLN7MsHn07s53nG/KImevWHSE6fEj4q/GihHw==}

  prosemirror-menu@1.2.4:
    resolution: {integrity: sha512-S/bXlc0ODQup6aiBbWVsX/eM+xJgCTAfMq/nLqaO5ID/am4wS0tTCIkzwytmao7ypEtjj39i7YbJjAgO20mIqA==}

  prosemirror-model@1.24.0:
    resolution: {integrity: sha512-Ft7epNnycoQSM+2ObF35SBbBX+5WY39v8amVlrtlAcpglhlHs2tCTnWl7RX5tbp/PsMKcRcWV9cXPuoBWq0AIQ==}

  prosemirror-schema-basic@1.2.3:
    resolution: {integrity: sha512-h+H0OQwZVqMon1PNn0AG9cTfx513zgIG2DY00eJ00Yvgb3UD+GQ/VlWW5rcaxacpCGT1Yx8nuhwXk4+QbXUfJA==}

  prosemirror-schema-list@1.5.0:
    resolution: {integrity: sha512-gg1tAfH1sqpECdhIHOA/aLg2VH3ROKBWQ4m8Qp9mBKrOxQRW61zc+gMCI8nh22gnBzd1t2u1/NPLmO3nAa3ssg==}

  prosemirror-state@1.4.3:
    resolution: {integrity: sha512-goFKORVbvPuAQaXhpbemJFRKJ2aixr+AZMGiquiqKxaucC6hlpHNZHWgz5R7dS4roHiwq9vDctE//CZ++o0W1Q==}

  prosemirror-tables@1.6.1:
    resolution: {integrity: sha512-p8WRJNA96jaNQjhJolmbxTzd6M4huRE5xQ8OxjvMhQUP0Nzpo4zz6TztEiwk6aoqGBhz9lxRWR1yRZLlpQN98w==}

  prosemirror-trailing-node@3.0.0:
    resolution: {integrity: sha512-xiun5/3q0w5eRnGYfNlW1uU9W6x5MoFKWwq/0TIRgt09lv7Hcser2QYV8t4muXbEr+Fwo0geYn79Xs4GKywrRQ==}
    peerDependencies:
      prosemirror-model: ^1.22.1
      prosemirror-state: ^1.4.2
      prosemirror-view: ^1.33.8

  prosemirror-transform@1.10.2:
    resolution: {integrity: sha512-2iUq0wv2iRoJO/zj5mv8uDUriOHWzXRnOTVgCzSXnktS/2iQRa3UUQwVlkBlYZFtygw6Nh1+X4mGqoYBINn5KQ==}

  prosemirror-view@1.37.0:
    resolution: {integrity: sha512-z2nkKI1sJzyi7T47Ji/ewBPuIma1RNvQCCYVdV+MqWBV7o4Sa1n94UJCJJ1aQRF/xRkFfyqLGlGFWitIcCOtbg==}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  prr@1.0.1:
    resolution: {integrity: sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==}

  punycode.js@2.3.1:
    resolution: {integrity: sha512-uxFIHU0YlHYhDQtV4R9J6a52SLx28BCjT+4ieh7IGbgwVJWO+km431c4yRlREUAsAmt/uMjQUyQHNEPf0M39CA==}
    engines: {node: '>=6'}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  qrcode.vue@3.6.0:
    resolution: {integrity: sha512-vQcl2fyHYHMjDO1GguCldJxepq2izQjBkDEEu9NENgfVKP6mv/e2SU62WbqYHGwTgWXLhxZ1NCD1dAZKHQq1fg==}
    peerDependencies:
      vue: ^3.0.0

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  rc9@2.1.2:
    resolution: {integrity: sha512-btXCnMmRIBINM2LDZoEmOogIZU7Qe7zn4BpomSKZ/ykbLObuBdvG+mFq11DL6fjH1DRwHhrlgtYWG96bJiC7Cg==}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  readdirp@4.0.2:
    resolution: {integrity: sha512-yDMz9g+VaZkqBYS/ozoBJwaBhTbZo3UNYQHNRw1D3UFQB8oHB4uS/tAODO+ZLjGWmUbKnIlOWO+aaIiAxrUWHA==}
    engines: {node: '>= 14.16.0'}

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==}

  regex-recursion@4.3.0:
    resolution: {integrity: sha512-5LcLnizwjcQ2ALfOj95MjcatxyqF5RPySx9yT+PaXu3Gox2vyAtLDjHB8NTJLtMGkvyau6nI3CfpwFCjPUIs/A==}

  regex-utilities@2.3.0:
    resolution: {integrity: sha512-8VhliFJAWRaUiVvREIiW2NXXTmHs4vMNnSzuJVhscgmGav3g9VDxLrQndI3dZZVVdp0ZO/5v0xmX516/7M9cng==}

  regex@5.0.2:
    resolution: {integrity: sha512-/pczGbKIQgfTMRV0XjABvc5RzLqQmwqxLHdQao2RTXPk+pmTXB2P0IaUHYdYyk412YLwUIkaeMd5T+RzVgTqnQ==}

  resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve-pkg-maps@1.0.0:
    resolution: {integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==}

  reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rfdc@1.4.1:
    resolution: {integrity: sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==}

  rollup@4.28.0:
    resolution: {integrity: sha512-G9GOrmgWHBma4YfCcX8PjH0qhXSdH8B4HDE2o4/jaxj93S4DPCIDoLcXz99eWMji4hB29UFCEd7B2gwGJDR9cQ==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  rope-sequence@1.3.4:
    resolution: {integrity: sha512-UT5EDe2cu2E/6O4igUr5PSFs23nvvukicWHx6GnOPlHAiiYbzNuCRQCuiUdHJQcqKalLKlrYJnjY0ySGsXNQXQ==}

  run-applescript@7.0.0:
    resolution: {integrity: sha512-9by4Ij99JUr/MCFBUkDKLWK3G9HVXmabKz9U5MlIAIuvuzkiOicRYs8XJLxX+xahD+mLiiCYDqF9dKAgtzKP1A==}
    engines: {node: '>=18'}

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  sass@1.82.0:
    resolution: {integrity: sha512-j4GMCTa8elGyN9A7x7bEglx0VgSpNUG4W4wNedQ33wSMdnkqQCT8HTwOaVSV4e6yQovcu/3Oc4coJP/l0xhL2Q==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  sax@1.4.1:
    resolution: {integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==}

  scroll-into-view-if-needed@2.2.31:
    resolution: {integrity: sha512-dGCXy99wZQivjmjIqihaBQNjryrz5rueJY7eHfTdyWEiR4ttYpsajb14rn9s5d4DY4EcY6+4+U/maARBXJedkA==}

  scule@1.3.0:
    resolution: {integrity: sha512-6FtHJEvt+pVMIB9IBY+IcCJ6Z5f1iQnytgyfKMhDKgmzYG+TeH/wx1y3l27rshSbLiSanrR9ffZDrEsmjlQF2g==}

  search-insights@2.17.3:
    resolution: {integrity: sha512-RQPdCYTa8A68uM2jwxoY842xDhvx3E5LFL1LxvxCNMev4o5mLuokczhzjAgGwUZBAmOKZknArSxLKmXtIi2AxQ==}

  semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.6.3:
    resolution: {integrity: sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==}
    engines: {node: '>=10'}
    hasBin: true

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  shiki@1.24.0:
    resolution: {integrity: sha512-qIneep7QRwxRd5oiHb8jaRzH15V/S8F3saCXOdjwRLgozZJr5x2yeBhQtqkO3FSzQDwYEFAYuifg4oHjpDghrg==}

  signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  simple-swizzle@0.2.2:
    resolution: {integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==}

  sirv@2.0.4:
    resolution: {integrity: sha512-94Bdh3cC2PKrbgSOUqTiGPWVZeSiXfKOVZNJniWoqrWrRkB1CJzBU3NEbiTsPcYy1lDsANA/THzS+9WBiy5nfQ==}
    engines: {node: '>= 10'}

  sirv@3.0.0:
    resolution: {integrity: sha512-BPwJGUeDaDCHihkORDchNyyTvWFhcusy1XMmhEVTQTwGeybFbp8YEmB+njbPnth1FibULBSBVwCQni25XlCUDg==}
    engines: {node: '>=18'}

  slash@5.1.0:
    resolution: {integrity: sha512-ZA6oR3T/pEyuqwMgAKT0/hAv8oAXckzbkmR0UkUosQ+Mc4RxGoJkRmwHgHufaenlyAgE1Mxgpdcrf75y6XcnDg==}
    engines: {node: '>=14.16'}

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  space-separated-tokens@2.0.2:
    resolution: {integrity: sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q==}

  speakingurl@14.0.1:
    resolution: {integrity: sha512-1POYv7uv2gXoyGFpBCmpDVSNV74IfsWlDW216UPjbWufNf+bSU6GdbDsxdcxtfwb4xlI3yxzOTKClUosxARYrQ==}
    engines: {node: '>=0.10.0'}

  std-env@3.8.0:
    resolution: {integrity: sha512-Bc3YwwCB+OzldMxOXJIIvC6cPRWr/LxOp48CdQTOkPyk/t4JWWJbrilwBd7RJzKV8QW7tJkcgAmeuLLJugl5/w==}

  stringify-entities@4.0.4:
    resolution: {integrity: sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==}

  strip-final-newline@3.0.0:
    resolution: {integrity: sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==}
    engines: {node: '>=12'}

  strip-final-newline@4.0.0:
    resolution: {integrity: sha512-aulFJcD6YK8V1G7iRB5tigAP4TsHBZZrOV8pjV++zdUwmeV8uzbY7yn6h9MswN62adStNZFuCIx4haBnRuMDaw==}
    engines: {node: '>=18'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  strip-literal@2.1.1:
    resolution: {integrity: sha512-631UJ6O00eNGfMiWG78ck80dfBab8X6IVFB51jZK5Icd7XAs60Z5y7QdSd/wGIklnWvRbUNloVzhOKKmutxQ6Q==}

  style-value-types@5.1.2:
    resolution: {integrity: sha512-Vs9fNreYF9j6W2VvuDTP7kepALi7sk0xtk2Tu8Yxi9UoajJdEVpNpCov0HsLTqXvNGKX+Uv09pkozVITi1jf3Q==}

  superjson@2.2.1:
    resolution: {integrity: sha512-8iGv75BYOa0xRJHK5vRLEjE2H/i4lulTjzpUXic3Eg8akftYjkmQDa8JARQ42rlczXyFR3IeRoeFCc7RxHsYZA==}
    engines: {node: '>=16'}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  svg-tags@1.0.0:
    resolution: {integrity: sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA==}

  synckit@0.9.2:
    resolution: {integrity: sha512-vrozgXDQwYO72vHjUb/HnFbQx1exDjoKzqx23aXEg2a9VIg2TSFZ8FmeZpTjUCFMYw7mpX4BE2SFu8wI7asYsw==}
    engines: {node: ^14.18.0 || >=16.0.0}

  tabbable@6.2.0:
    resolution: {integrity: sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==}

  tar@6.2.1:
    resolution: {integrity: sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==}
    engines: {node: '>=10'}

  terser@5.37.0:
    resolution: {integrity: sha512-B8wRRkmre4ERucLM/uXx4MOV5cbnOlVAqUst+1+iLKPI0dOgFO28f84ptoQt9HEI537PMzfYa/d+GEPKTRXmYA==}
    engines: {node: '>=10'}
    hasBin: true

  tinyexec@0.3.1:
    resolution: {integrity: sha512-WiCJLEECkO18gwqIp6+hJg0//p23HXp4S+gGtAKu3mI2F2/sXC4FvHvXvB0zJVVaTPhx1/tOwdbRsa1sOBIKqQ==}

  tinyglobby@0.2.10:
    resolution: {integrity: sha512-Zc+8eJlFMvgatPZTl6A9L/yht8QqdmUNtURHaKZLmKBE12hNPSrqNkUp2cs3M/UKmNVVAMFQYSjYIVHDjW5zew==}
    engines: {node: '>=12.0.0'}

  tippy.js@6.3.7:
    resolution: {integrity: sha512-E1d3oP2emgJ9dRQZdf3Kkn0qJgI6ZLpyS5z6ZkY1DF3kaQaBsGZsndEpHwx+eC+tYM41HaSNvNtLx8tU57FzTQ==}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  totalist@3.0.1:
    resolution: {integrity: sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==}
    engines: {node: '>=6'}

  trim-lines@3.0.1:
    resolution: {integrity: sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==}

  tslib@2.4.0:
    resolution: {integrity: sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  tsx@4.19.2:
    resolution: {integrity: sha512-pOUl6Vo2LUq/bSa8S5q7b91cgNSjctn9ugq/+Mvow99qW6x/UZYwzxy/******************************==}
    engines: {node: '>=18.0.0'}
    hasBin: true

  type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}

  type-fest@0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    engines: {node: '>=10'}

  typescript@5.7.2:
    resolution: {integrity: sha512-i5t66RHxDvVN40HfDd1PsEThGNnlMCMT3jMUuoh9/0TaqWevNontacunWyN02LA9/fIbEWlcHZcgTKb9QoaLfg==}
    engines: {node: '>=14.17'}
    hasBin: true

  uc.micro@1.0.6:
    resolution: {integrity: sha512-8Y75pvTYkLJW2hWQHXxoqRgV7qb9B+9vFEtidML+7koHUFapnVJAZ6cKs+Qjz5Aw3aZWHMC6u0wJE3At+nSGwA==}

  uc.micro@2.1.0:
    resolution: {integrity: sha512-ARDJmphmdvUk6Glw7y9DQ2bFkKBHwQHLi2lsaH6PPmz/Ka9sFOBsBluozhDltWmnv9u/cF6Rt87znRTPV+yp/A==}

  ufo@1.5.4:
    resolution: {integrity: sha512-UsUk3byDzKd04EyoZ7U4DOlxQaD14JUKQl6/P7wiX4FNvUfm3XL246n9W5AmqwW5RSFJ27NAuM0iLscAOYUiGQ==}

  unconfig@0.5.5:
    resolution: {integrity: sha512-VQZ5PT9HDX+qag0XdgQi8tJepPhXiR/yVOkn707gJDKo31lGjRilPREiQJ9Z6zd/Ugpv6ZvO5VxVIcatldYcNQ==}

  uncrypto@0.1.3:
    resolution: {integrity: sha512-Ql87qFHB3s/De2ClA9e0gsnS6zXG27SkTiSJwjCc9MebbfapQfuPzumMIUMi38ezPZVNFcHI9sUIepeQfw8J8Q==}

  unctx@2.3.1:
    resolution: {integrity: sha512-PhKke8ZYauiqh3FEMVNm7ljvzQiph0Mt3GBRve03IJm7ukfaON2OBK795tLwhbyfzknuRRkW0+Ze+CQUmzOZ+A==}

  undici-types@6.20.0:
    resolution: {integrity: sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==}

  unicorn-magic@0.1.0:
    resolution: {integrity: sha512-lRfVq8fE8gz6QMBuDM6a+LO3IAzTi05H6gCVaUpir2E1Rwpo4ZUog45KpNXKC/Mn3Yb9UDuHumeFTo9iV/D9FQ==}
    engines: {node: '>=18'}

  unicorn-magic@0.3.0:
    resolution: {integrity: sha512-+QBBXBCvifc56fsbuxZQ6Sic3wqqc3WWaqxs58gvJrcOuN83HGTCwz3oS5phzU9LthRNE9VrJCFCLUgHeeFnfA==}
    engines: {node: '>=18'}

  unimport@3.14.3:
    resolution: {integrity: sha512-yEJps4GW7jBdoQlxEV0ElBCJsJmH8FdZtk4oog0y++8hgLh0dGnDpE4oaTc0Lfx4N5rRJiGFUWHrBqC8CyUBmQ==}

  unist-util-is@6.0.0:
    resolution: {integrity: sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==}

  unist-util-position@5.0.0:
    resolution: {integrity: sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA==}

  unist-util-stringify-position@4.0.0:
    resolution: {integrity: sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==}

  unist-util-visit-parents@6.0.1:
    resolution: {integrity: sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==}

  unist-util-visit@5.0.0:
    resolution: {integrity: sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==}

  universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}

  unocss@0.63.6:
    resolution: {integrity: sha512-OKJJKEFWVz+Lsf3JdOgRiRtL+QOUQRBov89taUcCPFPZtrhP6pPVFCZHD9qMvY4IChMX7dzalQax3ZXJ3hbtkQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@unocss/webpack': 0.63.6
      vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0
    peerDependenciesMeta:
      '@unocss/webpack':
        optional: true
      vite:
        optional: true

  unplugin-auto-import@0.18.6:
    resolution: {integrity: sha512-LMFzX5DtkTj/3wZuyG5bgKBoJ7WSgzqSGJ8ppDRdlvPh45mx6t6w3OcbExQi53n3xF5MYkNGPNR/HYOL95KL2A==}
    engines: {node: '>=14'}
    peerDependencies:
      '@nuxt/kit': ^3.2.2
      '@vueuse/core': '*'
    peerDependenciesMeta:
      '@nuxt/kit':
        optional: true
      '@vueuse/core':
        optional: true

  unplugin-icons@0.20.2:
    resolution: {integrity: sha512-Ak6TKAiO812aIUrCelrBSTQbYC4FiqawnFrAusP/hjmB8f9cAug9jr381ItvLl+Asi4IVcjoOiPbpy9CfFGKvQ==}
    peerDependencies:
      '@svgr/core': '>=7.0.0'
      '@svgx/core': ^1.0.1
      '@vue/compiler-sfc': ^3.0.2 || ^2.7.0
      svelte: ^3.0.0 || ^4.0.0 || ^5.0.0
      vue-template-compiler: ^2.6.12
      vue-template-es2015-compiler: ^1.9.0
    peerDependenciesMeta:
      '@svgr/core':
        optional: true
      '@svgx/core':
        optional: true
      '@vue/compiler-sfc':
        optional: true
      svelte:
        optional: true
      vue-template-compiler:
        optional: true
      vue-template-es2015-compiler:
        optional: true

  unplugin-vue-components@0.27.5:
    resolution: {integrity: sha512-m9j4goBeNwXyNN8oZHHxvIIYiG8FQ9UfmKWeNllpDvhU7btKNNELGPt+o3mckQKuPwrE7e0PvCsx+IWuDSD9Vg==}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/parser': ^7.15.8
      '@nuxt/kit': ^3.2.2
      vue: 2 || 3
    peerDependenciesMeta:
      '@babel/parser':
        optional: true
      '@nuxt/kit':
        optional: true

  unplugin@1.16.0:
    resolution: {integrity: sha512-5liCNPuJW8dqh3+DM6uNM2EI3MLLpCKp/KY+9pB5M2S2SR2qvvDHhKgBOaTWEbZTAws3CXfB0rKTIolWKL05VQ==}
    engines: {node: '>=14.0.0'}

  untyped@1.5.1:
    resolution: {integrity: sha512-reBOnkJBFfBZ8pCKaeHgfZLcehXtM6UTxc+vqs1JvCps0c4amLNp3fhdGBZwYp+VLyoY9n3X5KOP7lCyWBUX9A==}
    hasBin: true

  update-browserslist-db@1.1.1:
    resolution: {integrity: sha512-R8UzCaa9Az+38REPiJ1tXlImTJXlVfgHZsglwBD/k6nj76ctsH1E3q4doGrukiLQd3sGQYu56r5+lo5r94l29A==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  uuid@11.0.3:
    resolution: {integrity: sha512-d0z310fCWv5dJwnX1Y/MncBAqGMKEzlBb1AOf7z9K8ALnd0utBX/msg/fA0+sbyN1ihbMsLhrBlnl1ak7Wa0rg==}
    hasBin: true

  vfile-message@4.0.2:
    resolution: {integrity: sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==}

  vfile@6.0.3:
    resolution: {integrity: sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==}

  vite-hot-client@0.2.4:
    resolution: {integrity: sha512-a1nzURqO7DDmnXqabFOliz908FRmIppkBKsJthS8rbe8hBEXwEwe4C3Pp33Z1JoFCYfVL4kTOMLKk0ZZxREIeA==}
    peerDependencies:
      vite: ^2.6.0 || ^3.0.0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0

  vite-plugin-inspect@0.8.8:
    resolution: {integrity: sha512-aZlBuXsWUPJFmMK92GIv6lH7LrwG2POu4KJ+aEdcqnu92OAf+rhBnfMDQvxIJPEB7hE2t5EyY/PMgf5aDLT8EA==}
    engines: {node: '>=14'}
    peerDependencies:
      '@nuxt/kit': '*'
      vite: ^3.1.0 || ^4.0.0 || ^5.0.0-0
    peerDependenciesMeta:
      '@nuxt/kit':
        optional: true

  vite-plugin-vue-devtools@7.6.7:
    resolution: {integrity: sha512-H1ZyjtpWjP5mHA5R15sQeYgAARuh2Myg3TDFXWZK6QOQRy8s3XjTIt319DogVjU/x3rC3L/jJQjIasRU04mWXA==}
    engines: {node: '>=v14.21.3'}
    peerDependencies:
      vite: ^3.1.0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.0-0

  vite-plugin-vue-inspector@5.3.1:
    resolution: {integrity: sha512-cBk172kZKTdvGpJuzCCLg8lJ909wopwsu3Ve9FsL1XsnLBiRT9U3MePcqrgGHgCX2ZgkqZmAGR8taxw+TV6s7A==}
    peerDependencies:
      vite: ^3.0.0-0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.0-0

  vite@5.4.11:
    resolution: {integrity: sha512-c7jFQRklXua0mTzneGW9QVyxFjUgwcihC4bXEtujIo2ouWCe1Ajt/amn2PCxYnhYfd5k09JX3SB7OYWFKYqj8Q==}
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || >=20.0.0
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true

  vitepress@1.0.0-rc.44:
    resolution: {integrity: sha512-tO5taxGI7fSpBK1D8zrZTyJJERlyU9nnt0jHSt3fywfq3VKn977Hg0wUuTkEmwXlFYwuW26+6+3xorf4nD3XvA==}
    hasBin: true
    peerDependencies:
      markdown-it-mathjax3: ^4.3.2
      postcss: ^8.4.35
    peerDependenciesMeta:
      markdown-it-mathjax3:
        optional: true
      postcss:
        optional: true

  vue-demi@0.14.10:
    resolution: {integrity: sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==}
    engines: {node: '>=12'}
    hasBin: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  vue-eslint-parser@9.4.3:
    resolution: {integrity: sha512-2rYRLWlIpaiN8xbPiDyXZXRgLGOtWxERV7ND5fFAv5qo1D2N9Fu9MNajBNc6o13lZ+24DAWCkQCvj4klgmcITg==}
    engines: {node: ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: '>=6.0.0'

  vue-flow-layout@0.0.5:
    resolution: {integrity: sha512-lZlqQ/Se1trGMtBMneZDWaiQiQBuxU8ivZ+KpJMem5zKROFpzuPq9KqyWABbSYbxq0qhqZs1I4DBwrY041rtOA==}

  vue-i18n@10.0.5:
    resolution: {integrity: sha512-9/gmDlCblz3i8ypu/afiIc/SUIfTTE1mr0mZhb9pk70xo2csHAM9mp2gdQ3KD2O0AM3Hz/5ypb+FycTj/lHlPQ==}
    engines: {node: '>= 16'}
    peerDependencies:
      vue: ^3.0.0

  vue-router@4.5.0:
    resolution: {integrity: sha512-HDuk+PuH5monfNuY+ct49mNmkCRK4xJAV9Ts4z9UFc4rzdDnxQLyCMGGc8pKhZhHTVzfanpNwB/lwqevcBwI4w==}
    peerDependencies:
      vue: ^3.2.0

  vue@3.5.13:
    resolution: {integrity: sha512-wmeiSMxkZCSc+PM2w2VRsOYAZC8GdipNFRTsLSfodVqI9mbejKeXEGr8SckuLnrQPGe3oJN5c3K0vpoU9q/wCQ==}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  w3c-keyname@2.2.8:
    resolution: {integrity: sha512-dpojBhNsCNN7T82Tm7k26A6G9ML3NkhDsnw9n/eoxSRlVBB4CEtIQ/KTCLI2Fwf3ataSXRhYFkQi3SlnFwPvPQ==}

  webpack-virtual-modules@0.6.2:
    resolution: {integrity: sha512-66/V2i5hQanC51vBQKPH4aI8NMAcBW59FVBs+rC7eGHupMyfn34q7rZIE+ETlJ+XTevqfUhVVBgSUNSW2flEUQ==}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}

  xml-name-validator@4.0.0:
    resolution: {integrity: sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==}
    engines: {node: '>=12'}

  y-prosemirror@1.3.6:
    resolution: {integrity: sha512-vtS2rv8+ll/TBQRqwUiqflgSuN/DhfvUQX0r5O3o5i0pO6K4pSNgFtVkOKtNWPBVkS6l9BDQjbtnDNftZnxq7Q==}
    engines: {node: '>=16.0.0', npm: '>=8.0.0'}
    peerDependencies:
      prosemirror-model: ^1.7.1
      prosemirror-state: ^1.2.3
      prosemirror-view: ^1.9.10
      y-protocols: ^1.0.1
      yjs: ^13.5.38

  y-protocols@1.0.6:
    resolution: {integrity: sha512-vHRF2L6iT3rwj1jub/K5tYcTT/mEYDUppgNPXwp8fmLpui9f7Yeq3OEtTLVF012j39QnV+KEQpNqoN7CWU7Y9Q==}
    engines: {node: '>=16.0.0', npm: '>=8.0.0'}
    peerDependencies:
      yjs: ^13.0.0

  y-websocket@3.0.0:
    resolution: {integrity: sha512-mUHy7AzkOZ834T/7piqtlA8Yk6AchqKqcrCXjKW8J1w2lPtRDjz8W5/CvXz9higKAHgKRKqpI3T33YkRFLkPtg==}
    engines: {node: '>=16.0.0', npm: '>=8.0.0'}
    peerDependencies:
      yjs: ^13.5.6

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}

  yjs@13.6.27:
    resolution: {integrity: sha512-OIDwaflOaq4wC6YlPBy2L6ceKeKuF7DeTxx+jPzv1FHn9tCZ0ZwSRnUBxD05E3yed46fv/FWJbvR+Ud7x0L7zw==}
    engines: {node: '>=16.0.0', npm: '>=8.0.0'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}

  yoctocolors@2.1.1:
    resolution: {integrity: sha512-GQHQqAopRhwU8Kt1DDM8NjibDXHC8eoh1erhGAJPEyveY9qqVeXvVikNKrDz69sHowPMorbPUrH/mx8c50eiBQ==}
    engines: {node: '>=18'}

  zwitch@2.0.4:
    resolution: {integrity: sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==}

snapshots:

  '@algolia/autocomplete-core@1.17.7(@algolia/client-search@5.15.0)(algoliasearch@5.15.0)(search-insights@2.17.3)':
    dependencies:
      '@algolia/autocomplete-plugin-algolia-insights': 1.17.7(@algolia/client-search@5.15.0)(algoliasearch@5.15.0)(search-insights@2.17.3)
      '@algolia/autocomplete-shared': 1.17.7(@algolia/client-search@5.15.0)(algoliasearch@5.15.0)
    transitivePeerDependencies:
      - '@algolia/client-search'
      - algoliasearch
      - search-insights

  '@algolia/autocomplete-plugin-algolia-insights@1.17.7(@algolia/client-search@5.15.0)(algoliasearch@5.15.0)(search-insights@2.17.3)':
    dependencies:
      '@algolia/autocomplete-shared': 1.17.7(@algolia/client-search@5.15.0)(algoliasearch@5.15.0)
      search-insights: 2.17.3
    transitivePeerDependencies:
      - '@algolia/client-search'
      - algoliasearch

  '@algolia/autocomplete-preset-algolia@1.17.7(@algolia/client-search@5.15.0)(algoliasearch@5.15.0)':
    dependencies:
      '@algolia/autocomplete-shared': 1.17.7(@algolia/client-search@5.15.0)(algoliasearch@5.15.0)
      '@algolia/client-search': 5.15.0
      algoliasearch: 5.15.0

  '@algolia/autocomplete-shared@1.17.7(@algolia/client-search@5.15.0)(algoliasearch@5.15.0)':
    dependencies:
      '@algolia/client-search': 5.15.0
      algoliasearch: 5.15.0

  '@algolia/client-abtesting@5.15.0':
    dependencies:
      '@algolia/client-common': 5.15.0
      '@algolia/requester-browser-xhr': 5.15.0
      '@algolia/requester-fetch': 5.15.0
      '@algolia/requester-node-http': 5.15.0

  '@algolia/client-analytics@5.15.0':
    dependencies:
      '@algolia/client-common': 5.15.0
      '@algolia/requester-browser-xhr': 5.15.0
      '@algolia/requester-fetch': 5.15.0
      '@algolia/requester-node-http': 5.15.0

  '@algolia/client-common@5.15.0': {}

  '@algolia/client-insights@5.15.0':
    dependencies:
      '@algolia/client-common': 5.15.0
      '@algolia/requester-browser-xhr': 5.15.0
      '@algolia/requester-fetch': 5.15.0
      '@algolia/requester-node-http': 5.15.0

  '@algolia/client-personalization@5.15.0':
    dependencies:
      '@algolia/client-common': 5.15.0
      '@algolia/requester-browser-xhr': 5.15.0
      '@algolia/requester-fetch': 5.15.0
      '@algolia/requester-node-http': 5.15.0

  '@algolia/client-query-suggestions@5.15.0':
    dependencies:
      '@algolia/client-common': 5.15.0
      '@algolia/requester-browser-xhr': 5.15.0
      '@algolia/requester-fetch': 5.15.0
      '@algolia/requester-node-http': 5.15.0

  '@algolia/client-search@5.15.0':
    dependencies:
      '@algolia/client-common': 5.15.0
      '@algolia/requester-browser-xhr': 5.15.0
      '@algolia/requester-fetch': 5.15.0
      '@algolia/requester-node-http': 5.15.0

  '@algolia/ingestion@1.15.0':
    dependencies:
      '@algolia/client-common': 5.15.0
      '@algolia/requester-browser-xhr': 5.15.0
      '@algolia/requester-fetch': 5.15.0
      '@algolia/requester-node-http': 5.15.0

  '@algolia/monitoring@1.15.0':
    dependencies:
      '@algolia/client-common': 5.15.0
      '@algolia/requester-browser-xhr': 5.15.0
      '@algolia/requester-fetch': 5.15.0
      '@algolia/requester-node-http': 5.15.0

  '@algolia/recommend@5.15.0':
    dependencies:
      '@algolia/client-common': 5.15.0
      '@algolia/requester-browser-xhr': 5.15.0
      '@algolia/requester-fetch': 5.15.0
      '@algolia/requester-node-http': 5.15.0

  '@algolia/requester-browser-xhr@5.15.0':
    dependencies:
      '@algolia/client-common': 5.15.0

  '@algolia/requester-fetch@5.15.0':
    dependencies:
      '@algolia/client-common': 5.15.0

  '@algolia/requester-node-http@5.15.0':
    dependencies:
      '@algolia/client-common': 5.15.0

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25

  '@antfu/install-pkg@0.4.1':
    dependencies:
      package-manager-detector: 0.2.7
      tinyexec: 0.3.1

  '@antfu/install-pkg@0.5.0':
    dependencies:
      package-manager-detector: 0.2.7
      tinyexec: 0.3.1

  '@antfu/utils@0.7.10': {}

  '@arco-design/color@0.4.0':
    dependencies:
      color: 3.2.1

  '@arco-design/web-vue@2.56.3(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      '@arco-design/color': 0.4.0
      b-tween: 0.3.3
      b-validate: 1.5.3
      compute-scroll-into-view: 1.0.20
      dayjs: 1.11.13
      number-precision: 1.6.0
      resize-observer-polyfill: 1.5.1
      scroll-into-view-if-needed: 2.2.31
      vue: 3.5.13(typescript@5.7.2)

  '@babel/code-frame@7.26.2':
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.26.3': {}

  '@babel/core@7.26.0':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.26.3
      '@babel/helper-compilation-targets': 7.25.9
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.0)
      '@babel/helpers': 7.26.0
      '@babel/parser': 7.26.3
      '@babel/template': 7.25.9
      '@babel/traverse': 7.26.4
      '@babel/types': 7.26.3
      convert-source-map: 2.0.0
      debug: 4.3.7
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.26.3':
    dependencies:
      '@babel/parser': 7.26.3
      '@babel/types': 7.26.3
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.0.2

  '@babel/helper-annotate-as-pure@7.25.9':
    dependencies:
      '@babel/types': 7.26.3

  '@babel/helper-compilation-targets@7.25.9':
    dependencies:
      '@babel/compat-data': 7.26.3
      '@babel/helper-validator-option': 7.25.9
      browserslist: 4.24.2
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/helper-replace-supers': 7.25.9(@babel/core@7.26.0)
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/traverse': 7.26.4
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-member-expression-to-functions@7.25.9':
    dependencies:
      '@babel/traverse': 7.26.4
      '@babel/types': 7.26.3
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-imports@7.25.9':
    dependencies:
      '@babel/traverse': 7.26.4
      '@babel/types': 7.26.3
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9
      '@babel/traverse': 7.26.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-optimise-call-expression@7.25.9':
    dependencies:
      '@babel/types': 7.26.3

  '@babel/helper-plugin-utils@7.25.9': {}

  '@babel/helper-replace-supers@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/traverse': 7.26.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    dependencies:
      '@babel/traverse': 7.26.4
      '@babel/types': 7.26.3
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.25.9': {}

  '@babel/helper-validator-identifier@7.25.9': {}

  '@babel/helper-validator-option@7.25.9': {}

  '@babel/helpers@7.26.0':
    dependencies:
      '@babel/template': 7.25.9
      '@babel/types': 7.26.3

  '@babel/parser@7.26.3':
    dependencies:
      '@babel/types': 7.26.3

  '@babel/plugin-proposal-decorators@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-create-class-features-plugin': 7.25.9(@babel/core@7.26.0)
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/plugin-syntax-decorators': 7.25.9(@babel/core@7.26.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-syntax-decorators@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-syntax-import-attributes@7.26.0(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-syntax-jsx@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-syntax-typescript@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-typescript@7.26.3(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-create-class-features-plugin': 7.25.9(@babel/core@7.26.0)
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/plugin-syntax-typescript': 7.25.9(@babel/core@7.26.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/runtime@7.26.0':
    dependencies:
      regenerator-runtime: 0.14.1

  '@babel/standalone@7.26.4':
    optional: true

  '@babel/template@7.25.9':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/parser': 7.26.3
      '@babel/types': 7.26.3

  '@babel/traverse@7.26.4':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.26.3
      '@babel/parser': 7.26.3
      '@babel/template': 7.25.9
      '@babel/types': 7.26.3
      debug: 4.3.7
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.26.3':
    dependencies:
      '@babel/helper-string-parser': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9

  '@docsearch/css@3.8.0': {}

  '@docsearch/js@3.8.0(@algolia/client-search@5.15.0)(search-insights@2.17.3)':
    dependencies:
      '@docsearch/react': 3.8.0(@algolia/client-search@5.15.0)(search-insights@2.17.3)
      preact: 10.25.1
    transitivePeerDependencies:
      - '@algolia/client-search'
      - '@types/react'
      - react
      - react-dom
      - search-insights

  '@docsearch/react@3.8.0(@algolia/client-search@5.15.0)(search-insights@2.17.3)':
    dependencies:
      '@algolia/autocomplete-core': 1.17.7(@algolia/client-search@5.15.0)(algoliasearch@5.15.0)(search-insights@2.17.3)
      '@algolia/autocomplete-preset-algolia': 1.17.7(@algolia/client-search@5.15.0)(algoliasearch@5.15.0)
      '@docsearch/css': 3.8.0
      algoliasearch: 5.15.0
    optionalDependencies:
      search-insights: 2.17.3
    transitivePeerDependencies:
      - '@algolia/client-search'

  '@esbuild/aix-ppc64@0.21.5':
    optional: true

  '@esbuild/aix-ppc64@0.23.1':
    optional: true

  '@esbuild/android-arm64@0.21.5':
    optional: true

  '@esbuild/android-arm64@0.23.1':
    optional: true

  '@esbuild/android-arm@0.21.5':
    optional: true

  '@esbuild/android-arm@0.23.1':
    optional: true

  '@esbuild/android-x64@0.21.5':
    optional: true

  '@esbuild/android-x64@0.23.1':
    optional: true

  '@esbuild/darwin-arm64@0.21.5':
    optional: true

  '@esbuild/darwin-arm64@0.23.1':
    optional: true

  '@esbuild/darwin-x64@0.21.5':
    optional: true

  '@esbuild/darwin-x64@0.23.1':
    optional: true

  '@esbuild/freebsd-arm64@0.21.5':
    optional: true

  '@esbuild/freebsd-arm64@0.23.1':
    optional: true

  '@esbuild/freebsd-x64@0.21.5':
    optional: true

  '@esbuild/freebsd-x64@0.23.1':
    optional: true

  '@esbuild/linux-arm64@0.21.5':
    optional: true

  '@esbuild/linux-arm64@0.23.1':
    optional: true

  '@esbuild/linux-arm@0.21.5':
    optional: true

  '@esbuild/linux-arm@0.23.1':
    optional: true

  '@esbuild/linux-ia32@0.21.5':
    optional: true

  '@esbuild/linux-ia32@0.23.1':
    optional: true

  '@esbuild/linux-loong64@0.21.5':
    optional: true

  '@esbuild/linux-loong64@0.23.1':
    optional: true

  '@esbuild/linux-mips64el@0.21.5':
    optional: true

  '@esbuild/linux-mips64el@0.23.1':
    optional: true

  '@esbuild/linux-ppc64@0.21.5':
    optional: true

  '@esbuild/linux-ppc64@0.23.1':
    optional: true

  '@esbuild/linux-riscv64@0.21.5':
    optional: true

  '@esbuild/linux-riscv64@0.23.1':
    optional: true

  '@esbuild/linux-s390x@0.21.5':
    optional: true

  '@esbuild/linux-s390x@0.23.1':
    optional: true

  '@esbuild/linux-x64@0.21.5':
    optional: true

  '@esbuild/linux-x64@0.23.1':
    optional: true

  '@esbuild/netbsd-x64@0.21.5':
    optional: true

  '@esbuild/netbsd-x64@0.23.1':
    optional: true

  '@esbuild/openbsd-arm64@0.23.1':
    optional: true

  '@esbuild/openbsd-x64@0.21.5':
    optional: true

  '@esbuild/openbsd-x64@0.23.1':
    optional: true

  '@esbuild/sunos-x64@0.21.5':
    optional: true

  '@esbuild/sunos-x64@0.23.1':
    optional: true

  '@esbuild/win32-arm64@0.21.5':
    optional: true

  '@esbuild/win32-arm64@0.23.1':
    optional: true

  '@esbuild/win32-ia32@0.21.5':
    optional: true

  '@esbuild/win32-ia32@0.23.1':
    optional: true

  '@esbuild/win32-x64@0.21.5':
    optional: true

  '@esbuild/win32-x64@0.23.1':
    optional: true

  '@eslint-community/eslint-utils@4.4.1(eslint@9.16.0(jiti@2.4.1))':
    dependencies:
      eslint: 9.16.0(jiti@2.4.1)
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/config-array@0.19.1':
    dependencies:
      '@eslint/object-schema': 2.1.5
      debug: 4.3.7
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/core@0.9.1':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/eslintrc@3.2.0':
    dependencies:
      ajv: 6.12.6
      debug: 4.3.7
      espree: 10.3.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@9.16.0': {}

  '@eslint/object-schema@2.1.5': {}

  '@eslint/plugin-kit@0.2.4':
    dependencies:
      levn: 0.4.1

  '@fingerprintjs/fingerprintjs@4.6.2':
    dependencies:
      tslib: 2.8.1

  '@floating-ui/core@1.6.8':
    dependencies:
      '@floating-ui/utils': 0.2.8

  '@floating-ui/dom@1.6.12':
    dependencies:
      '@floating-ui/core': 1.6.8
      '@floating-ui/utils': 0.2.8

  '@floating-ui/utils@0.2.8': {}

  '@flypeng/tool@5.5.4':
    dependencies:
      dayjs: 1.11.13

  '@humanfs/core@0.19.1': {}

  '@humanfs/node@0.16.6':
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.3.1

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/retry@0.3.1': {}

  '@humanwhocodes/retry@0.4.1': {}

  '@iconify-json/lucide@1.2.17':
    dependencies:
      '@iconify/types': 2.0.0

  '@iconify-json/tabler@1.2.10':
    dependencies:
      '@iconify/types': 2.0.0

  '@iconify/types@2.0.0': {}

  '@iconify/utils@2.1.33':
    dependencies:
      '@antfu/install-pkg': 0.4.1
      '@antfu/utils': 0.7.10
      '@iconify/types': 2.0.0
      debug: 4.3.7
      kolorist: 1.8.0
      local-pkg: 0.5.1
      mlly: 1.7.3
    transitivePeerDependencies:
      - supports-color

  '@iconify/vue@4.2.0(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      '@iconify/types': 2.0.0
      vue: 3.5.13(typescript@5.7.2)

  '@intlify/core-base@10.0.5':
    dependencies:
      '@intlify/message-compiler': 10.0.5
      '@intlify/shared': 10.0.5

  '@intlify/message-compiler@10.0.5':
    dependencies:
      '@intlify/shared': 10.0.5
      source-map-js: 1.2.1

  '@intlify/shared@10.0.5': {}

  '@jridgewell/gen-mapping@0.3.5':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/source-map@0.3.6':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
    optional: true

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.17.1

  '@nuxt/kit@3.14.1592(rollup@4.28.0)':
    dependencies:
      '@nuxt/schema': 3.14.1592(rollup@4.28.0)
      c12: 2.0.1
      consola: 3.2.3
      defu: 6.1.4
      destr: 2.0.3
      globby: 14.0.2
      hash-sum: 2.0.0
      ignore: 6.0.2
      jiti: 2.4.1
      klona: 2.0.6
      knitwork: 1.1.0
      mlly: 1.7.3
      pathe: 1.1.2
      pkg-types: 1.2.1
      scule: 1.3.0
      semver: 7.6.3
      ufo: 1.5.4
      unctx: 2.3.1
      unimport: 3.14.3(rollup@4.28.0)
      untyped: 1.5.1
    transitivePeerDependencies:
      - magicast
      - rollup
      - supports-color
    optional: true

  '@nuxt/schema@3.14.1592(rollup@4.28.0)':
    dependencies:
      c12: 2.0.1
      compatx: 0.1.8
      consola: 3.2.3
      defu: 6.1.4
      hookable: 5.5.3
      pathe: 1.1.2
      pkg-types: 1.2.1
      scule: 1.3.0
      std-env: 3.8.0
      ufo: 1.5.4
      uncrypto: 0.1.3
      unimport: 3.14.3(rollup@4.28.0)
      untyped: 1.5.1
    transitivePeerDependencies:
      - magicast
      - rollup
      - supports-color
    optional: true

  '@parcel/watcher-android-arm64@2.5.0':
    optional: true

  '@parcel/watcher-darwin-arm64@2.5.0':
    optional: true

  '@parcel/watcher-darwin-x64@2.5.0':
    optional: true

  '@parcel/watcher-freebsd-x64@2.5.0':
    optional: true

  '@parcel/watcher-linux-arm-glibc@2.5.0':
    optional: true

  '@parcel/watcher-linux-arm-musl@2.5.0':
    optional: true

  '@parcel/watcher-linux-arm64-glibc@2.5.0':
    optional: true

  '@parcel/watcher-linux-arm64-musl@2.5.0':
    optional: true

  '@parcel/watcher-linux-x64-glibc@2.5.0':
    optional: true

  '@parcel/watcher-linux-x64-musl@2.5.0':
    optional: true

  '@parcel/watcher-win32-arm64@2.5.0':
    optional: true

  '@parcel/watcher-win32-ia32@2.5.0':
    optional: true

  '@parcel/watcher-win32-x64@2.5.0':
    optional: true

  '@parcel/watcher@2.5.0':
    dependencies:
      detect-libc: 1.0.3
      is-glob: 4.0.3
      micromatch: 4.0.8
      node-addon-api: 7.1.1
    optionalDependencies:
      '@parcel/watcher-android-arm64': 2.5.0
      '@parcel/watcher-darwin-arm64': 2.5.0
      '@parcel/watcher-darwin-x64': 2.5.0
      '@parcel/watcher-freebsd-x64': 2.5.0
      '@parcel/watcher-linux-arm-glibc': 2.5.0
      '@parcel/watcher-linux-arm-musl': 2.5.0
      '@parcel/watcher-linux-arm64-glibc': 2.5.0
      '@parcel/watcher-linux-arm64-musl': 2.5.0
      '@parcel/watcher-linux-x64-glibc': 2.5.0
      '@parcel/watcher-linux-x64-musl': 2.5.0
      '@parcel/watcher-win32-arm64': 2.5.0
      '@parcel/watcher-win32-ia32': 2.5.0
      '@parcel/watcher-win32-x64': 2.5.0
    optional: true

  '@pkgr/core@0.1.1': {}

  '@polka/url@1.0.0-next.28': {}

  '@popperjs/core@2.11.8': {}

  '@remirror/core-constants@3.0.0': {}

  '@rollup/pluginutils@5.1.3(rollup@4.28.0)':
    dependencies:
      '@types/estree': 1.0.6
      estree-walker: 2.0.2
      picomatch: 4.0.2
    optionalDependencies:
      rollup: 4.28.0

  '@rollup/rollup-android-arm-eabi@4.28.0':
    optional: true

  '@rollup/rollup-android-arm64@4.28.0':
    optional: true

  '@rollup/rollup-darwin-arm64@4.28.0':
    optional: true

  '@rollup/rollup-darwin-x64@4.28.0':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.28.0':
    optional: true

  '@rollup/rollup-freebsd-x64@4.28.0':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.28.0':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.28.0':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.28.0':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.28.0':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.28.0':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.28.0':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.28.0':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.28.0':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.28.0':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.28.0':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.28.0':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.28.0':
    optional: true

  '@sec-ant/readable-stream@0.4.1': {}

  '@shikijs/core@1.24.0':
    dependencies:
      '@shikijs/engine-javascript': 1.24.0
      '@shikijs/engine-oniguruma': 1.24.0
      '@shikijs/types': 1.24.0
      '@shikijs/vscode-textmate': 9.3.0
      '@types/hast': 3.0.4
      hast-util-to-html: 9.0.3

  '@shikijs/engine-javascript@1.24.0':
    dependencies:
      '@shikijs/types': 1.24.0
      '@shikijs/vscode-textmate': 9.3.0
      oniguruma-to-es: 0.7.0

  '@shikijs/engine-oniguruma@1.24.0':
    dependencies:
      '@shikijs/types': 1.24.0
      '@shikijs/vscode-textmate': 9.3.0

  '@shikijs/transformers@1.24.0':
    dependencies:
      shiki: 1.24.0

  '@shikijs/types@1.24.0':
    dependencies:
      '@shikijs/vscode-textmate': 9.3.0
      '@types/hast': 3.0.4

  '@shikijs/vscode-textmate@9.3.0': {}

  '@sindresorhus/merge-streams@2.3.0':
    optional: true

  '@sindresorhus/merge-streams@4.0.0': {}

  '@tiptap/core@2.10.3(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/pm': 2.10.3

  '@tiptap/core@2.26.1(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-bold@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-bold@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-bubble-menu@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3
      tippy.js: 6.3.7

  '@tiptap/extension-bubble-menu@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3
      tippy.js: 6.3.7

  '@tiptap/extension-bubble-menu@3.0.7(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@floating-ui/dom': 1.6.12
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3
    optional: true

  '@tiptap/extension-bullet-list@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-bullet-list@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-character-count@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-character-count@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-code-block@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-code-block@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-code@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-code@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-collaboration-cursor@2.22.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(y-prosemirror@1.3.6(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0)(y-protocols@1.0.6(yjs@13.6.27))(yjs@13.6.27))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)
      y-prosemirror: 1.3.6(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0)(y-protocols@1.0.6(yjs@13.6.27))(yjs@13.6.27)

  '@tiptap/extension-collaboration-cursor@2.22.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(y-prosemirror@1.3.6(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0)(y-protocols@1.0.6(yjs@13.6.27))(yjs@13.6.27))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      y-prosemirror: 1.3.6(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0)(y-protocols@1.0.6(yjs@13.6.27))(yjs@13.6.27)

  '@tiptap/extension-collaboration@2.22.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)(y-prosemirror@1.3.6(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0)(y-protocols@1.0.6(yjs@13.6.27))(yjs@13.6.27))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3
      y-prosemirror: 1.3.6(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0)(y-protocols@1.0.6(yjs@13.6.27))(yjs@13.6.27)

  '@tiptap/extension-collaboration@2.22.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)(y-prosemirror@1.3.6(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0)(y-protocols@1.0.6(yjs@13.6.27))(yjs@13.6.27))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3
      y-prosemirror: 1.3.6(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0)(y-protocols@1.0.6(yjs@13.6.27))(yjs@13.6.27)

  '@tiptap/extension-document@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-document@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-dropcursor@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-dropcursor@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-floating-menu@3.0.7(@floating-ui/dom@1.6.12)(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@floating-ui/dom': 1.6.12
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3
    optional: true

  '@tiptap/extension-gapcursor@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-gapcursor@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-image@2.22.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-italic@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-italic@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-link@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3
      linkifyjs: 4.2.0

  '@tiptap/extension-link@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3
      linkifyjs: 4.2.0

  '@tiptap/extension-list-item@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-list-item@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-mention@3.0.7(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)(@tiptap/suggestion@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3
      '@tiptap/suggestion': 2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)

  '@tiptap/extension-ordered-list@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-ordered-list@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-placeholder@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-placeholder@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-strike@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-strike@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-subscript@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-subscript@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-superscript@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-superscript@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-table-cell@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-table-cell@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-table-header@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-table-header@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-table-row@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-table-row@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-table@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-table@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-task-item@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-task-item@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/extension-task-list@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-task-list@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-text@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-text@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-typography@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-typography@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/extension-underline@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)

  '@tiptap/extension-underline@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)

  '@tiptap/pm@2.10.3':
    dependencies:
      prosemirror-changeset: 2.2.1
      prosemirror-collab: 1.3.1
      prosemirror-commands: 1.6.2
      prosemirror-dropcursor: 1.8.1
      prosemirror-gapcursor: 1.3.2
      prosemirror-history: 1.4.1
      prosemirror-inputrules: 1.4.0
      prosemirror-keymap: 1.2.2
      prosemirror-markdown: 1.13.1
      prosemirror-menu: 1.2.4
      prosemirror-model: 1.24.0
      prosemirror-schema-basic: 1.2.3
      prosemirror-schema-list: 1.5.0
      prosemirror-state: 1.4.3
      prosemirror-tables: 1.6.1
      prosemirror-trailing-node: 3.0.0(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0)
      prosemirror-transform: 1.10.2
      prosemirror-view: 1.37.0

  '@tiptap/suggestion@2.10.3(@tiptap/core@2.10.3(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.10.3(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/suggestion@2.10.3(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)':
    dependencies:
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3

  '@tiptap/vue-3@3.0.7(@floating-ui/dom@1.6.12)(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      '@floating-ui/dom': 1.6.12
      '@tiptap/core': 2.26.1(@tiptap/pm@2.10.3)
      '@tiptap/pm': 2.10.3
      vue: 3.5.13(typescript@5.7.2)
    optionalDependencies:
      '@tiptap/extension-bubble-menu': 3.0.7(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)
      '@tiptap/extension-floating-menu': 3.0.7(@floating-ui/dom@1.6.12)(@tiptap/core@2.26.1(@tiptap/pm@2.10.3))(@tiptap/pm@2.10.3)

  '@types/eslint@9.6.1':
    dependencies:
      '@types/estree': 1.0.6
      '@types/json-schema': 7.0.15
    optional: true

  '@types/estree@1.0.6': {}

  '@types/hast@3.0.4':
    dependencies:
      '@types/unist': 3.0.3

  '@types/json-schema@7.0.15': {}

  '@types/linkify-it@3.0.5': {}

  '@types/linkify-it@5.0.0': {}

  '@types/markdown-it@13.0.9':
    dependencies:
      '@types/linkify-it': 3.0.5
      '@types/mdurl': 1.0.5

  '@types/markdown-it@14.1.2':
    dependencies:
      '@types/linkify-it': 5.0.0
      '@types/mdurl': 2.0.0

  '@types/mdast@4.0.4':
    dependencies:
      '@types/unist': 3.0.3

  '@types/mdurl@1.0.5': {}

  '@types/mdurl@2.0.0': {}

  '@types/node@22.10.1':
    dependencies:
      undici-types: 6.20.0
    optional: true

  '@types/unist@3.0.3': {}

  '@types/web-bluetooth@0.0.20': {}

  '@ungap/structured-clone@1.2.0': {}

  '@unocss/astro@0.63.6(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))':
    dependencies:
      '@unocss/core': 0.63.6
      '@unocss/reset': 0.63.6
      '@unocss/vite': 0.63.6(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))
    optionalDependencies:
      vite: 5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0)
    transitivePeerDependencies:
      - rollup
      - supports-color
      - typescript

  '@unocss/astro@0.63.6(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))':
    dependencies:
      '@unocss/core': 0.63.6
      '@unocss/reset': 0.63.6
      '@unocss/vite': 0.63.6(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))
    optionalDependencies:
      vite: 5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)
    transitivePeerDependencies:
      - rollup
      - supports-color
      - typescript

  '@unocss/cli@0.63.6(rollup@4.28.0)':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@rollup/pluginutils': 5.1.3(rollup@4.28.0)
      '@unocss/config': 0.63.6
      '@unocss/core': 0.63.6
      '@unocss/preset-uno': 0.63.6
      cac: 6.7.14
      chokidar: 3.6.0
      colorette: 2.0.20
      consola: 3.2.3
      magic-string: 0.30.14
      pathe: 1.1.2
      perfect-debounce: 1.0.0
      tinyglobby: 0.2.10
    transitivePeerDependencies:
      - rollup
      - supports-color

  '@unocss/config@0.63.6':
    dependencies:
      '@unocss/core': 0.63.6
      unconfig: 0.5.5
    transitivePeerDependencies:
      - supports-color

  '@unocss/core@0.63.6': {}

  '@unocss/extractor-arbitrary-variants@0.63.6':
    dependencies:
      '@unocss/core': 0.63.6

  '@unocss/inspector@0.63.6(typescript@5.7.2)':
    dependencies:
      '@unocss/core': 0.63.6
      '@unocss/rule-utils': 0.63.6
      gzip-size: 6.0.0
      sirv: 2.0.4
      vue-flow-layout: 0.0.5(typescript@5.7.2)
    transitivePeerDependencies:
      - typescript

  '@unocss/postcss@0.63.6(postcss@8.4.49)':
    dependencies:
      '@unocss/config': 0.63.6
      '@unocss/core': 0.63.6
      '@unocss/rule-utils': 0.63.6
      css-tree: 3.0.1
      postcss: 8.4.49
      tinyglobby: 0.2.10
    transitivePeerDependencies:
      - supports-color

  '@unocss/preset-attributify@0.63.6':
    dependencies:
      '@unocss/core': 0.63.6

  '@unocss/preset-icons@0.63.6':
    dependencies:
      '@iconify/utils': 2.1.33
      '@unocss/core': 0.63.6
      ofetch: 1.4.1
    transitivePeerDependencies:
      - supports-color

  '@unocss/preset-mini@0.63.6':
    dependencies:
      '@unocss/core': 0.63.6
      '@unocss/extractor-arbitrary-variants': 0.63.6
      '@unocss/rule-utils': 0.63.6

  '@unocss/preset-tagify@0.63.6':
    dependencies:
      '@unocss/core': 0.63.6

  '@unocss/preset-typography@0.63.6':
    dependencies:
      '@unocss/core': 0.63.6
      '@unocss/preset-mini': 0.63.6

  '@unocss/preset-uno@0.63.6':
    dependencies:
      '@unocss/core': 0.63.6
      '@unocss/preset-mini': 0.63.6
      '@unocss/preset-wind': 0.63.6
      '@unocss/rule-utils': 0.63.6

  '@unocss/preset-web-fonts@0.63.6':
    dependencies:
      '@unocss/core': 0.63.6
      ofetch: 1.4.1

  '@unocss/preset-wind@0.63.6':
    dependencies:
      '@unocss/core': 0.63.6
      '@unocss/preset-mini': 0.63.6
      '@unocss/rule-utils': 0.63.6

  '@unocss/reset@0.63.6': {}

  '@unocss/rule-utils@0.63.6':
    dependencies:
      '@unocss/core': 0.63.6
      magic-string: 0.30.14

  '@unocss/transformer-attributify-jsx@0.63.6':
    dependencies:
      '@unocss/core': 0.63.6

  '@unocss/transformer-compile-class@0.63.6':
    dependencies:
      '@unocss/core': 0.63.6

  '@unocss/transformer-directives@0.63.6':
    dependencies:
      '@unocss/core': 0.63.6
      '@unocss/rule-utils': 0.63.6
      css-tree: 3.0.1

  '@unocss/transformer-variant-group@0.63.6':
    dependencies:
      '@unocss/core': 0.63.6

  '@unocss/vite@0.63.6(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@rollup/pluginutils': 5.1.3(rollup@4.28.0)
      '@unocss/config': 0.63.6
      '@unocss/core': 0.63.6
      '@unocss/inspector': 0.63.6(typescript@5.7.2)
      chokidar: 3.6.0
      magic-string: 0.30.14
      tinyglobby: 0.2.10
      vite: 5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0)
    transitivePeerDependencies:
      - rollup
      - supports-color
      - typescript

  '@unocss/vite@0.63.6(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@rollup/pluginutils': 5.1.3(rollup@4.28.0)
      '@unocss/config': 0.63.6
      '@unocss/core': 0.63.6
      '@unocss/inspector': 0.63.6(typescript@5.7.2)
      chokidar: 3.6.0
      magic-string: 0.30.14
      tinyglobby: 0.2.10
      vite: 5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)
    transitivePeerDependencies:
      - rollup
      - supports-color
      - typescript

  '@vitejs/plugin-vue-jsx@4.1.1(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/plugin-transform-typescript': 7.26.3(@babel/core@7.26.0)
      '@vue/babel-plugin-jsx': 1.2.5(@babel/core@7.26.0)
      vite: 5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0)
      vue: 3.5.13(typescript@5.7.2)
    transitivePeerDependencies:
      - supports-color

  '@vitejs/plugin-vue-jsx@4.1.1(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/plugin-transform-typescript': 7.26.3(@babel/core@7.26.0)
      '@vue/babel-plugin-jsx': 1.2.5(@babel/core@7.26.0)
      vite: 5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)
      vue: 3.5.13(typescript@5.7.2)
    transitivePeerDependencies:
      - supports-color

  '@vitejs/plugin-vue@5.2.1(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      vite: 5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0)
      vue: 3.5.13(typescript@5.7.2)

  '@vitejs/plugin-vue@5.2.1(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      vite: 5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)
      vue: 3.5.13(typescript@5.7.2)

  '@vitepress-demo-preview/component@2.3.2(vitepress@1.0.0-rc.44(@algolia/client-search@5.15.0)(@types/node@22.10.1)(postcss@8.4.49)(sass@1.82.0)(search-insights@2.17.3)(terser@5.37.0)(typescript@5.7.2))(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      vitepress: 1.0.0-rc.44(@algolia/client-search@5.15.0)(@types/node@22.10.1)(postcss@8.4.49)(sass@1.82.0)(search-insights@2.17.3)(terser@5.37.0)(typescript@5.7.2)
      vue: 3.5.13(typescript@5.7.2)

  '@vitepress-demo-preview/plugin@1.2.3(markdown-it-container@3.0.0)(vitepress@1.0.0-rc.44(@algolia/client-search@5.15.0)(@types/node@22.10.1)(postcss@8.4.49)(sass@1.82.0)(search-insights@2.17.3)(terser@5.37.0)(typescript@5.7.2))(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      '@flypeng/tool': 5.5.4
      markdown-it: 13.0.2
      markdown-it-container: 3.0.0
      vitepress: 1.0.0-rc.44(@algolia/client-search@5.15.0)(@types/node@22.10.1)(postcss@8.4.49)(sass@1.82.0)(search-insights@2.17.3)(terser@5.37.0)(typescript@5.7.2)
      vue: 3.5.13(typescript@5.7.2)

  '@vue/babel-helper-vue-transform-on@1.2.5': {}

  '@vue/babel-plugin-jsx@1.2.5(@babel/core@7.26.0)':
    dependencies:
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.0)
      '@babel/template': 7.25.9
      '@babel/traverse': 7.26.4
      '@babel/types': 7.26.3
      '@vue/babel-helper-vue-transform-on': 1.2.5
      '@vue/babel-plugin-resolve-type': 1.2.5(@babel/core@7.26.0)
      html-tags: 3.3.1
      svg-tags: 1.0.0
    optionalDependencies:
      '@babel/core': 7.26.0
    transitivePeerDependencies:
      - supports-color

  '@vue/babel-plugin-resolve-type@1.2.5(@babel/core@7.26.0)':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/core': 7.26.0
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/parser': 7.26.3
      '@vue/compiler-sfc': 3.5.13
    transitivePeerDependencies:
      - supports-color

  '@vue/compiler-core@3.5.13':
    dependencies:
      '@babel/parser': 7.26.3
      '@vue/shared': 3.5.13
      entities: 4.5.0
      estree-walker: 2.0.2
      source-map-js: 1.2.1

  '@vue/compiler-dom@3.5.13':
    dependencies:
      '@vue/compiler-core': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/compiler-sfc@3.5.13':
    dependencies:
      '@babel/parser': 7.26.3
      '@vue/compiler-core': 3.5.13
      '@vue/compiler-dom': 3.5.13
      '@vue/compiler-ssr': 3.5.13
      '@vue/shared': 3.5.13
      estree-walker: 2.0.2
      magic-string: 0.30.14
      postcss: 8.4.49
      source-map-js: 1.2.1

  '@vue/compiler-ssr@3.5.13':
    dependencies:
      '@vue/compiler-dom': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/devtools-api@6.6.4': {}

  '@vue/devtools-api@7.6.7':
    dependencies:
      '@vue/devtools-kit': 7.6.7

  '@vue/devtools-core@7.6.7(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      '@vue/devtools-kit': 7.6.7
      '@vue/devtools-shared': 7.6.7
      mitt: 3.0.1
      nanoid: 5.0.9
      pathe: 1.1.2
      vite-hot-client: 0.2.4(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))
      vue: 3.5.13(typescript@5.7.2)
    transitivePeerDependencies:
      - vite

  '@vue/devtools-core@7.6.7(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      '@vue/devtools-kit': 7.6.7
      '@vue/devtools-shared': 7.6.7
      mitt: 3.0.1
      nanoid: 5.0.9
      pathe: 1.1.2
      vite-hot-client: 0.2.4(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))
      vue: 3.5.13(typescript@5.7.2)
    transitivePeerDependencies:
      - vite

  '@vue/devtools-kit@7.6.7':
    dependencies:
      '@vue/devtools-shared': 7.6.7
      birpc: 0.2.19
      hookable: 5.5.3
      mitt: 3.0.1
      perfect-debounce: 1.0.0
      speakingurl: 14.0.1
      superjson: 2.2.1

  '@vue/devtools-shared@7.6.7':
    dependencies:
      rfdc: 1.4.1

  '@vue/eslint-config-prettier@10.1.0(@types/eslint@9.6.1)(eslint@9.16.0(jiti@2.4.1))(prettier@3.4.2)':
    dependencies:
      eslint: 9.16.0(jiti@2.4.1)
      eslint-config-prettier: 9.1.0(eslint@9.16.0(jiti@2.4.1))
      eslint-plugin-prettier: 5.2.1(@types/eslint@9.6.1)(eslint-config-prettier@9.1.0(eslint@9.16.0(jiti@2.4.1)))(eslint@9.16.0(jiti@2.4.1))(prettier@3.4.2)
      prettier: 3.4.2
    transitivePeerDependencies:
      - '@types/eslint'

  '@vue/reactivity@3.5.13':
    dependencies:
      '@vue/shared': 3.5.13

  '@vue/runtime-core@3.5.13':
    dependencies:
      '@vue/reactivity': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/runtime-dom@3.5.13':
    dependencies:
      '@vue/reactivity': 3.5.13
      '@vue/runtime-core': 3.5.13
      '@vue/shared': 3.5.13
      csstype: 3.1.3

  '@vue/server-renderer@3.5.13(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      '@vue/compiler-ssr': 3.5.13
      '@vue/shared': 3.5.13
      vue: 3.5.13(typescript@5.7.2)

  '@vue/shared@3.5.13': {}

  '@vueuse/core@10.11.1(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      '@types/web-bluetooth': 0.0.20
      '@vueuse/metadata': 10.11.1
      '@vueuse/shared': 10.11.1(vue@3.5.13(typescript@5.7.2))
      vue-demi: 0.14.10(vue@3.5.13(typescript@5.7.2))
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  '@vueuse/core@11.3.0(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      '@types/web-bluetooth': 0.0.20
      '@vueuse/metadata': 11.3.0
      '@vueuse/shared': 11.3.0(vue@3.5.13(typescript@5.7.2))
      vue-demi: 0.14.10(vue@3.5.13(typescript@5.7.2))
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  '@vueuse/integrations@10.11.1(focus-trap@7.6.2)(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      '@vueuse/core': 10.11.1(vue@3.5.13(typescript@5.7.2))
      '@vueuse/shared': 10.11.1(vue@3.5.13(typescript@5.7.2))
      vue-demi: 0.14.10(vue@3.5.13(typescript@5.7.2))
    optionalDependencies:
      focus-trap: 7.6.2
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  '@vueuse/metadata@10.11.1': {}

  '@vueuse/metadata@11.3.0': {}

  '@vueuse/motion@2.2.6(rollup@4.28.0)(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      '@vueuse/core': 10.11.1(vue@3.5.13(typescript@5.7.2))
      '@vueuse/shared': 10.11.1(vue@3.5.13(typescript@5.7.2))
      csstype: 3.1.3
      framesync: 6.1.2
      popmotion: 11.0.5
      style-value-types: 5.1.2
      vue: 3.5.13(typescript@5.7.2)
    optionalDependencies:
      '@nuxt/kit': 3.14.1592(rollup@4.28.0)
    transitivePeerDependencies:
      - '@vue/composition-api'
      - magicast
      - rollup
      - supports-color

  '@vueuse/shared@10.11.1(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      vue-demi: 0.14.10(vue@3.5.13(typescript@5.7.2))
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  '@vueuse/shared@11.3.0(vue@3.5.13(typescript@5.7.2))':
    dependencies:
      vue-demi: 0.14.10(vue@3.5.13(typescript@5.7.2))
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  acorn-jsx@5.3.2(acorn@8.14.0):
    dependencies:
      acorn: 8.14.0

  acorn@8.14.0: {}

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  algoliasearch@5.15.0:
    dependencies:
      '@algolia/client-abtesting': 5.15.0
      '@algolia/client-analytics': 5.15.0
      '@algolia/client-common': 5.15.0
      '@algolia/client-insights': 5.15.0
      '@algolia/client-personalization': 5.15.0
      '@algolia/client-query-suggestions': 5.15.0
      '@algolia/client-search': 5.15.0
      '@algolia/ingestion': 1.15.0
      '@algolia/monitoring': 1.15.0
      '@algolia/recommend': 5.15.0
      '@algolia/requester-browser-xhr': 5.15.0
      '@algolia/requester-fetch': 5.15.0
      '@algolia/requester-node-http': 5.15.0

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  argparse@2.0.1: {}

  asynckit@0.4.0: {}

  axios@1.10.0:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.3
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  b-tween@0.3.3: {}

  b-validate@1.5.3: {}

  balanced-match@1.0.2: {}

  binary-extensions@2.3.0: {}

  birpc@0.2.19: {}

  boolbase@1.0.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.24.2:
    dependencies:
      caniuse-lite: 1.0.30001686
      electron-to-chromium: 1.5.71
      node-releases: 2.0.18
      update-browserslist-db: 1.1.1(browserslist@4.24.2)

  buffer-from@1.1.2:
    optional: true

  bundle-name@4.1.0:
    dependencies:
      run-applescript: 7.0.0

  bundle-require@5.0.0(esbuild@0.23.1):
    dependencies:
      esbuild: 0.23.1
      load-tsconfig: 0.2.5

  c12@2.0.1:
    dependencies:
      chokidar: 4.0.1
      confbox: 0.1.8
      defu: 6.1.4
      dotenv: 16.4.7
      giget: 1.2.3
      jiti: 2.4.1
      mlly: 1.7.3
      ohash: 1.1.4
      pathe: 1.1.2
      perfect-debounce: 1.0.0
      pkg-types: 1.2.1
      rc9: 2.1.2
    optional: true

  cac@6.7.14: {}

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  callsites@3.1.0: {}

  caniuse-lite@1.0.30001686: {}

  ccount@2.0.1: {}

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  character-entities-html4@2.1.0: {}

  character-entities-legacy@3.0.0: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chokidar@4.0.1:
    dependencies:
      readdirp: 4.0.2

  chownr@2.0.0:
    optional: true

  citty@0.1.6:
    dependencies:
      consola: 3.2.3
    optional: true

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  color-string@1.9.1:
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2

  color@3.2.1:
    dependencies:
      color-convert: 1.9.3
      color-string: 1.9.1

  colorette@2.0.20: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  comma-separated-tokens@2.0.3: {}

  commander@2.20.3:
    optional: true

  compatx@0.1.8:
    optional: true

  compute-scroll-into-view@1.0.20: {}

  concat-map@0.0.1: {}

  confbox@0.1.8: {}

  consola@3.2.3: {}

  convert-source-map@2.0.0: {}

  copy-anything@2.0.6:
    dependencies:
      is-what: 3.14.1
    optional: true

  copy-anything@3.0.5:
    dependencies:
      is-what: 4.1.16

  crelt@1.0.6: {}

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  css-tree@3.0.1:
    dependencies:
      mdn-data: 2.12.1
      source-map-js: 1.2.1

  cssesc@3.0.0: {}

  csstype@3.1.3: {}

  dayjs@1.11.13: {}

  debug@4.3.7:
    dependencies:
      ms: 2.1.3

  deep-is@0.1.4: {}

  default-browser-id@5.0.0: {}

  default-browser@5.2.1:
    dependencies:
      bundle-name: 4.1.0
      default-browser-id: 5.0.0

  define-lazy-prop@3.0.0: {}

  defu@6.1.4: {}

  delayed-stream@1.0.0: {}

  dequal@2.0.3: {}

  destr@2.0.3: {}

  detect-libc@1.0.3:
    optional: true

  devlop@1.1.0:
    dependencies:
      dequal: 2.0.3

  dotenv@16.4.7:
    optional: true

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  duplexer@0.1.2: {}

  electron-to-chromium@1.5.71: {}

  emoji-regex-xs@1.0.0: {}

  entities@3.0.1: {}

  entities@4.5.0: {}

  errno@0.1.8:
    dependencies:
      prr: 1.0.1
    optional: true

  error-stack-parser-es@0.1.5: {}

  es-define-property@1.0.1: {}

  es-drager@1.3.0(vue@3.5.13(typescript@5.7.2)):
    dependencies:
      vue: 3.5.13(typescript@5.7.2)

  es-errors@1.3.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  esbuild@0.21.5:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.21.5
      '@esbuild/android-arm': 0.21.5
      '@esbuild/android-arm64': 0.21.5
      '@esbuild/android-x64': 0.21.5
      '@esbuild/darwin-arm64': 0.21.5
      '@esbuild/darwin-x64': 0.21.5
      '@esbuild/freebsd-arm64': 0.21.5
      '@esbuild/freebsd-x64': 0.21.5
      '@esbuild/linux-arm': 0.21.5
      '@esbuild/linux-arm64': 0.21.5
      '@esbuild/linux-ia32': 0.21.5
      '@esbuild/linux-loong64': 0.21.5
      '@esbuild/linux-mips64el': 0.21.5
      '@esbuild/linux-ppc64': 0.21.5
      '@esbuild/linux-riscv64': 0.21.5
      '@esbuild/linux-s390x': 0.21.5
      '@esbuild/linux-x64': 0.21.5
      '@esbuild/netbsd-x64': 0.21.5
      '@esbuild/openbsd-x64': 0.21.5
      '@esbuild/sunos-x64': 0.21.5
      '@esbuild/win32-arm64': 0.21.5
      '@esbuild/win32-ia32': 0.21.5
      '@esbuild/win32-x64': 0.21.5

  esbuild@0.23.1:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.23.1
      '@esbuild/android-arm': 0.23.1
      '@esbuild/android-arm64': 0.23.1
      '@esbuild/android-x64': 0.23.1
      '@esbuild/darwin-arm64': 0.23.1
      '@esbuild/darwin-x64': 0.23.1
      '@esbuild/freebsd-arm64': 0.23.1
      '@esbuild/freebsd-x64': 0.23.1
      '@esbuild/linux-arm': 0.23.1
      '@esbuild/linux-arm64': 0.23.1
      '@esbuild/linux-ia32': 0.23.1
      '@esbuild/linux-loong64': 0.23.1
      '@esbuild/linux-mips64el': 0.23.1
      '@esbuild/linux-ppc64': 0.23.1
      '@esbuild/linux-riscv64': 0.23.1
      '@esbuild/linux-s390x': 0.23.1
      '@esbuild/linux-x64': 0.23.1
      '@esbuild/netbsd-x64': 0.23.1
      '@esbuild/openbsd-arm64': 0.23.1
      '@esbuild/openbsd-x64': 0.23.1
      '@esbuild/sunos-x64': 0.23.1
      '@esbuild/win32-arm64': 0.23.1
      '@esbuild/win32-ia32': 0.23.1
      '@esbuild/win32-x64': 0.23.1

  escalade@3.2.0: {}

  escape-string-regexp@4.0.0: {}

  escape-string-regexp@5.0.0: {}

  eslint-config-prettier@9.1.0(eslint@9.16.0(jiti@2.4.1)):
    dependencies:
      eslint: 9.16.0(jiti@2.4.1)

  eslint-plugin-prettier@5.2.1(@types/eslint@9.6.1)(eslint-config-prettier@9.1.0(eslint@9.16.0(jiti@2.4.1)))(eslint@9.16.0(jiti@2.4.1))(prettier@3.4.2):
    dependencies:
      eslint: 9.16.0(jiti@2.4.1)
      prettier: 3.4.2
      prettier-linter-helpers: 1.0.0
      synckit: 0.9.2
    optionalDependencies:
      '@types/eslint': 9.6.1
      eslint-config-prettier: 9.1.0(eslint@9.16.0(jiti@2.4.1))

  eslint-plugin-vue@9.32.0(eslint@9.16.0(jiti@2.4.1)):
    dependencies:
      '@eslint-community/eslint-utils': 4.4.1(eslint@9.16.0(jiti@2.4.1))
      eslint: 9.16.0(jiti@2.4.1)
      globals: 13.24.0
      natural-compare: 1.4.0
      nth-check: 2.1.1
      postcss-selector-parser: 6.1.2
      semver: 7.6.3
      vue-eslint-parser: 9.4.3(eslint@9.16.0(jiti@2.4.1))
      xml-name-validator: 4.0.0
    transitivePeerDependencies:
      - supports-color

  eslint-scope@7.2.2:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-scope@8.2.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.0: {}

  eslint@9.16.0(jiti@2.4.1):
    dependencies:
      '@eslint-community/eslint-utils': 4.4.1(eslint@9.16.0(jiti@2.4.1))
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.19.1
      '@eslint/core': 0.9.1
      '@eslint/eslintrc': 3.2.0
      '@eslint/js': 9.16.0
      '@eslint/plugin-kit': 0.2.4
      '@humanfs/node': 0.16.6
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.1
      '@types/estree': 1.0.6
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.3.7
      escape-string-regexp: 4.0.0
      eslint-scope: 8.2.0
      eslint-visitor-keys: 4.2.0
      espree: 10.3.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    optionalDependencies:
      jiti: 2.4.1
    transitivePeerDependencies:
      - supports-color

  espree@10.3.0:
    dependencies:
      acorn: 8.14.0
      acorn-jsx: 5.3.2(acorn@8.14.0)
      eslint-visitor-keys: 4.2.0

  espree@9.6.1:
    dependencies:
      acorn: 8.14.0
      acorn-jsx: 5.3.2(acorn@8.14.0)
      eslint-visitor-keys: 3.4.3

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@5.3.0: {}

  estree-walker@2.0.2: {}

  estree-walker@3.0.3:
    dependencies:
      '@types/estree': 1.0.6

  esutils@2.0.3: {}

  execa@8.0.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 8.0.1
      human-signals: 5.0.0
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.3.0
      onetime: 6.0.0
      signal-exit: 4.1.0
      strip-final-newline: 3.0.0
    optional: true

  execa@9.5.1:
    dependencies:
      '@sindresorhus/merge-streams': 4.0.0
      cross-spawn: 7.0.6
      figures: 6.1.0
      get-stream: 9.0.1
      human-signals: 8.0.0
      is-plain-obj: 4.1.0
      is-stream: 4.0.1
      npm-run-path: 6.0.0
      pretty-ms: 9.2.0
      signal-exit: 4.1.0
      strip-final-newline: 4.0.0
      yoctocolors: 2.1.1

  fast-deep-equal@3.1.3: {}

  fast-diff@1.3.0: {}

  fast-glob@3.3.2:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fastq@1.17.1:
    dependencies:
      reusify: 1.0.4

  fdir@6.4.2(picomatch@4.0.2):
    optionalDependencies:
      picomatch: 4.0.2

  figures@6.1.0:
    dependencies:
      is-unicode-supported: 2.1.0

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.2
      keyv: 4.5.4

  flatted@3.3.2: {}

  focus-trap@7.6.2:
    dependencies:
      tabbable: 6.2.0

  follow-redirects@1.15.9: {}

  form-data@4.0.3:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      hasown: 2.0.2
      mime-types: 2.1.35

  framesync@6.1.2:
    dependencies:
      tslib: 2.4.0

  fs-extra@11.2.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs-minipass@2.1.0:
    dependencies:
      minipass: 3.3.6
    optional: true

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  gensync@1.0.0-beta.2: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-stream@8.0.1:
    optional: true

  get-stream@9.0.1:
    dependencies:
      '@sec-ant/readable-stream': 0.4.1
      is-stream: 4.0.1

  get-tsconfig@4.8.1:
    dependencies:
      resolve-pkg-maps: 1.0.0

  giget@1.2.3:
    dependencies:
      citty: 0.1.6
      consola: 3.2.3
      defu: 6.1.4
      node-fetch-native: 1.6.4
      nypm: 0.3.12
      ohash: 1.1.4
      pathe: 1.1.2
      tar: 6.2.1
    optional: true

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  globals@11.12.0: {}

  globals@13.24.0:
    dependencies:
      type-fest: 0.20.2

  globals@14.0.0: {}

  globby@14.0.2:
    dependencies:
      '@sindresorhus/merge-streams': 2.3.0
      fast-glob: 3.3.2
      ignore: 5.3.2
      path-type: 5.0.0
      slash: 5.1.0
      unicorn-magic: 0.1.0
    optional: true

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  gzip-size@6.0.0:
    dependencies:
      duplexer: 0.1.2

  has-flag@4.0.0: {}

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hash-sum@2.0.0:
    optional: true

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  hast-util-to-html@9.0.3:
    dependencies:
      '@types/hast': 3.0.4
      '@types/unist': 3.0.3
      ccount: 2.0.1
      comma-separated-tokens: 2.0.3
      hast-util-whitespace: 3.0.0
      html-void-elements: 3.0.0
      mdast-util-to-hast: 13.2.0
      property-information: 6.5.0
      space-separated-tokens: 2.0.2
      stringify-entities: 4.0.4
      zwitch: 2.0.4

  hast-util-whitespace@3.0.0:
    dependencies:
      '@types/hast': 3.0.4

  hey-listen@1.0.8: {}

  hookable@5.5.3: {}

  html-tags@3.3.1: {}

  html-void-elements@3.0.0: {}

  human-signals@5.0.0:
    optional: true

  human-signals@8.0.0: {}

  i18next@23.16.8:
    dependencies:
      '@babel/runtime': 7.26.0

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2
    optional: true

  ignore@5.3.2: {}

  ignore@6.0.2:
    optional: true

  image-size@0.5.5:
    optional: true

  immutable@5.0.3: {}

  import-fresh@3.3.0:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  importx@0.4.4:
    dependencies:
      bundle-require: 5.0.0(esbuild@0.23.1)
      debug: 4.3.7
      esbuild: 0.23.1
      jiti: 2.0.0-beta.3
      jiti-v1: jiti@1.21.6
      pathe: 1.1.2
      tsx: 4.19.2
    transitivePeerDependencies:
      - supports-color

  imurmurhash@0.1.4: {}

  is-arrayish@0.3.2: {}

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-docker@3.0.0: {}

  is-extglob@2.1.1: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-inside-container@1.0.0:
    dependencies:
      is-docker: 3.0.0

  is-number@7.0.0: {}

  is-plain-obj@4.1.0: {}

  is-stream@3.0.0:
    optional: true

  is-stream@4.0.1: {}

  is-unicode-supported@2.1.0: {}

  is-what@3.14.1:
    optional: true

  is-what@4.1.16: {}

  is-wsl@3.1.0:
    dependencies:
      is-inside-container: 1.0.0

  isexe@2.0.0: {}

  isomorphic.js@0.2.5: {}

  jiti@1.21.6: {}

  jiti@2.0.0-beta.3: {}

  jiti@2.4.1:
    optional: true

  js-tokens@4.0.0: {}

  js-tokens@9.0.1: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsesc@3.0.2: {}

  json-buffer@3.0.1: {}

  json-schema-traverse@0.4.1: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@2.2.3: {}

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  klona@2.0.6:
    optional: true

  knitwork@1.1.0:
    optional: true

  kolorist@1.8.0: {}

  less@4.4.0:
    dependencies:
      copy-anything: 2.0.6
      parse-node-version: 1.0.1
      tslib: 2.8.1
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.11
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      needle: 3.3.1
      source-map: 0.6.1
    optional: true

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lib0@0.2.109:
    dependencies:
      isomorphic.js: 0.2.5

  linkify-it@4.0.1:
    dependencies:
      uc.micro: 1.0.6

  linkify-it@5.0.0:
    dependencies:
      uc.micro: 2.1.0

  linkifyjs@4.2.0: {}

  load-tsconfig@0.2.5: {}

  local-pkg@0.5.1:
    dependencies:
      mlly: 1.7.3
      pkg-types: 1.2.1

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash.merge@4.6.2: {}

  lodash@4.17.21: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  magic-string@0.30.14:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0

  make-dir@2.1.0:
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    optional: true

  mark.js@8.11.1: {}

  markdown-it-container@3.0.0: {}

  markdown-it@13.0.2:
    dependencies:
      argparse: 2.0.1
      entities: 3.0.1
      linkify-it: 4.0.1
      mdurl: 1.0.1
      uc.micro: 1.0.6

  markdown-it@14.1.0:
    dependencies:
      argparse: 2.0.1
      entities: 4.5.0
      linkify-it: 5.0.0
      mdurl: 2.0.0
      punycode.js: 2.3.1
      uc.micro: 2.1.0

  math-intrinsics@1.1.0: {}

  mdast-util-to-hast@13.2.0:
    dependencies:
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      '@ungap/structured-clone': 1.2.0
      devlop: 1.1.0
      micromark-util-sanitize-uri: 2.0.1
      trim-lines: 3.0.1
      unist-util-position: 5.0.0
      unist-util-visit: 5.0.0
      vfile: 6.0.3

  mdn-data@2.12.1: {}

  mdurl@1.0.1: {}

  mdurl@2.0.0: {}

  merge-stream@2.0.0:
    optional: true

  merge2@1.4.1: {}

  micromark-util-character@2.1.1:
    dependencies:
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.1

  micromark-util-encode@2.0.1: {}

  micromark-util-sanitize-uri@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-encode: 2.0.1
      micromark-util-symbol: 2.0.1

  micromark-util-symbol@2.0.1: {}

  micromark-util-types@2.0.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0:
    optional: true

  mimic-fn@4.0.0:
    optional: true

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  minipass@3.3.6:
    dependencies:
      yallist: 4.0.0
    optional: true

  minipass@5.0.0:
    optional: true

  minisearch@6.3.0: {}

  minizlib@2.1.2:
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0
    optional: true

  mitt@3.0.1: {}

  mkdirp@1.0.4:
    optional: true

  mlly@1.7.3:
    dependencies:
      acorn: 8.14.0
      pathe: 1.1.2
      pkg-types: 1.2.1
      ufo: 1.5.4

  mri@1.2.0:
    optional: true

  mrmime@2.0.0: {}

  ms@2.1.3: {}

  nanoid@3.3.8: {}

  nanoid@5.0.9: {}

  natural-compare@1.4.0: {}

  needle@3.3.1:
    dependencies:
      iconv-lite: 0.6.3
      sax: 1.4.1
    optional: true

  node-addon-api@7.1.1:
    optional: true

  node-fetch-native@1.6.4: {}

  node-releases@2.0.18: {}

  normalize-path@3.0.0: {}

  npm-run-path@5.3.0:
    dependencies:
      path-key: 4.0.0
    optional: true

  npm-run-path@6.0.0:
    dependencies:
      path-key: 4.0.0
      unicorn-magic: 0.3.0

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  number-precision@1.6.0: {}

  nypm@0.3.12:
    dependencies:
      citty: 0.1.6
      consola: 3.2.3
      execa: 8.0.1
      pathe: 1.1.2
      pkg-types: 1.2.1
      ufo: 1.5.4
    optional: true

  ofetch@1.4.1:
    dependencies:
      destr: 2.0.3
      node-fetch-native: 1.6.4
      ufo: 1.5.4

  ohash@1.1.4:
    optional: true

  onetime@6.0.0:
    dependencies:
      mimic-fn: 4.0.0
    optional: true

  oniguruma-to-es@0.7.0:
    dependencies:
      emoji-regex-xs: 1.0.0
      regex: 5.0.2
      regex-recursion: 4.3.0

  open@10.1.0:
    dependencies:
      default-browser: 5.2.1
      define-lazy-prop: 3.0.0
      is-inside-container: 1.0.0
      is-wsl: 3.1.0

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  orderedmap@2.1.1: {}

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  package-manager-detector@0.2.7: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-ms@4.0.0: {}

  parse-node-version@1.0.1:
    optional: true

  path-exists@4.0.0: {}

  path-key@3.1.1: {}

  path-key@4.0.0: {}

  path-type@5.0.0:
    optional: true

  pathe@1.1.2: {}

  perfect-debounce@1.0.0: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.2: {}

  pify@4.0.1:
    optional: true

  pinia@2.3.0(typescript@5.7.2)(vue@3.5.13(typescript@5.7.2)):
    dependencies:
      '@vue/devtools-api': 6.6.4
      vue: 3.5.13(typescript@5.7.2)
      vue-demi: 0.14.10(vue@3.5.13(typescript@5.7.2))
    optionalDependencies:
      typescript: 5.7.2
    transitivePeerDependencies:
      - '@vue/composition-api'

  pkg-types@1.2.1:
    dependencies:
      confbox: 0.1.8
      mlly: 1.7.3
      pathe: 1.1.2

  popmotion@11.0.5:
    dependencies:
      framesync: 6.1.2
      hey-listen: 1.0.8
      style-value-types: 5.1.2
      tslib: 2.4.0

  postcss-selector-parser@6.1.2:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss@8.4.49:
    dependencies:
      nanoid: 3.3.8
      picocolors: 1.1.1
      source-map-js: 1.2.1

  preact@10.25.1: {}

  prelude-ls@1.2.1: {}

  prettier-linter-helpers@1.0.0:
    dependencies:
      fast-diff: 1.3.0

  prettier@3.4.2: {}

  pretty-ms@9.2.0:
    dependencies:
      parse-ms: 4.0.0

  property-information@6.5.0: {}

  prosemirror-changeset@2.2.1:
    dependencies:
      prosemirror-transform: 1.10.2

  prosemirror-collab@1.3.1:
    dependencies:
      prosemirror-state: 1.4.3

  prosemirror-commands@1.6.2:
    dependencies:
      prosemirror-model: 1.24.0
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.2

  prosemirror-dropcursor@1.8.1:
    dependencies:
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.2
      prosemirror-view: 1.37.0

  prosemirror-gapcursor@1.3.2:
    dependencies:
      prosemirror-keymap: 1.2.2
      prosemirror-model: 1.24.0
      prosemirror-state: 1.4.3
      prosemirror-view: 1.37.0

  prosemirror-history@1.4.1:
    dependencies:
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.2
      prosemirror-view: 1.37.0
      rope-sequence: 1.3.4

  prosemirror-inputrules@1.4.0:
    dependencies:
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.2

  prosemirror-keymap@1.2.2:
    dependencies:
      prosemirror-state: 1.4.3
      w3c-keyname: 2.2.8

  prosemirror-markdown@1.13.1:
    dependencies:
      '@types/markdown-it': 14.1.2
      markdown-it: 14.1.0
      prosemirror-model: 1.24.0

  prosemirror-menu@1.2.4:
    dependencies:
      crelt: 1.0.6
      prosemirror-commands: 1.6.2
      prosemirror-history: 1.4.1
      prosemirror-state: 1.4.3

  prosemirror-model@1.24.0:
    dependencies:
      orderedmap: 2.1.1

  prosemirror-schema-basic@1.2.3:
    dependencies:
      prosemirror-model: 1.24.0

  prosemirror-schema-list@1.5.0:
    dependencies:
      prosemirror-model: 1.24.0
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.2

  prosemirror-state@1.4.3:
    dependencies:
      prosemirror-model: 1.24.0
      prosemirror-transform: 1.10.2
      prosemirror-view: 1.37.0

  prosemirror-tables@1.6.1:
    dependencies:
      prosemirror-keymap: 1.2.2
      prosemirror-model: 1.24.0
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.2
      prosemirror-view: 1.37.0

  prosemirror-trailing-node@3.0.0(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0):
    dependencies:
      '@remirror/core-constants': 3.0.0
      escape-string-regexp: 4.0.0
      prosemirror-model: 1.24.0
      prosemirror-state: 1.4.3
      prosemirror-view: 1.37.0

  prosemirror-transform@1.10.2:
    dependencies:
      prosemirror-model: 1.24.0

  prosemirror-view@1.37.0:
    dependencies:
      prosemirror-model: 1.24.0
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.2

  proxy-from-env@1.1.0: {}

  prr@1.0.1:
    optional: true

  punycode.js@2.3.1: {}

  punycode@2.3.1: {}

  qrcode.vue@3.6.0(vue@3.5.13(typescript@5.7.2)):
    dependencies:
      vue: 3.5.13(typescript@5.7.2)

  queue-microtask@1.2.3: {}

  rc9@2.1.2:
    dependencies:
      defu: 6.1.4
      destr: 2.0.3
    optional: true

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  readdirp@4.0.2: {}

  regenerator-runtime@0.14.1: {}

  regex-recursion@4.3.0:
    dependencies:
      regex-utilities: 2.3.0

  regex-utilities@2.3.0: {}

  regex@5.0.2:
    dependencies:
      regex-utilities: 2.3.0

  resize-observer-polyfill@1.5.1: {}

  resolve-from@4.0.0: {}

  resolve-pkg-maps@1.0.0: {}

  reusify@1.0.4: {}

  rfdc@1.4.1: {}

  rollup@4.28.0:
    dependencies:
      '@types/estree': 1.0.6
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.28.0
      '@rollup/rollup-android-arm64': 4.28.0
      '@rollup/rollup-darwin-arm64': 4.28.0
      '@rollup/rollup-darwin-x64': 4.28.0
      '@rollup/rollup-freebsd-arm64': 4.28.0
      '@rollup/rollup-freebsd-x64': 4.28.0
      '@rollup/rollup-linux-arm-gnueabihf': 4.28.0
      '@rollup/rollup-linux-arm-musleabihf': 4.28.0
      '@rollup/rollup-linux-arm64-gnu': 4.28.0
      '@rollup/rollup-linux-arm64-musl': 4.28.0
      '@rollup/rollup-linux-powerpc64le-gnu': 4.28.0
      '@rollup/rollup-linux-riscv64-gnu': 4.28.0
      '@rollup/rollup-linux-s390x-gnu': 4.28.0
      '@rollup/rollup-linux-x64-gnu': 4.28.0
      '@rollup/rollup-linux-x64-musl': 4.28.0
      '@rollup/rollup-win32-arm64-msvc': 4.28.0
      '@rollup/rollup-win32-ia32-msvc': 4.28.0
      '@rollup/rollup-win32-x64-msvc': 4.28.0
      fsevents: 2.3.3

  rope-sequence@1.3.4: {}

  run-applescript@7.0.0: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  safer-buffer@2.1.2:
    optional: true

  sass@1.82.0:
    dependencies:
      chokidar: 4.0.1
      immutable: 5.0.3
      source-map-js: 1.2.1
    optionalDependencies:
      '@parcel/watcher': 2.5.0

  sax@1.4.1:
    optional: true

  scroll-into-view-if-needed@2.2.31:
    dependencies:
      compute-scroll-into-view: 1.0.20

  scule@1.3.0: {}

  search-insights@2.17.3: {}

  semver@5.7.2:
    optional: true

  semver@6.3.1: {}

  semver@7.6.3: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shiki@1.24.0:
    dependencies:
      '@shikijs/core': 1.24.0
      '@shikijs/engine-javascript': 1.24.0
      '@shikijs/engine-oniguruma': 1.24.0
      '@shikijs/types': 1.24.0
      '@shikijs/vscode-textmate': 9.3.0
      '@types/hast': 3.0.4

  signal-exit@4.1.0: {}

  simple-swizzle@0.2.2:
    dependencies:
      is-arrayish: 0.3.2

  sirv@2.0.4:
    dependencies:
      '@polka/url': 1.0.0-next.28
      mrmime: 2.0.0
      totalist: 3.0.1

  sirv@3.0.0:
    dependencies:
      '@polka/url': 1.0.0-next.28
      mrmime: 2.0.0
      totalist: 3.0.1

  slash@5.1.0:
    optional: true

  source-map-js@1.2.1: {}

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1
    optional: true

  source-map@0.6.1:
    optional: true

  space-separated-tokens@2.0.2: {}

  speakingurl@14.0.1: {}

  std-env@3.8.0:
    optional: true

  stringify-entities@4.0.4:
    dependencies:
      character-entities-html4: 2.1.0
      character-entities-legacy: 3.0.0

  strip-final-newline@3.0.0:
    optional: true

  strip-final-newline@4.0.0: {}

  strip-json-comments@3.1.1: {}

  strip-literal@2.1.1:
    dependencies:
      js-tokens: 9.0.1

  style-value-types@5.1.2:
    dependencies:
      hey-listen: 1.0.8
      tslib: 2.4.0

  superjson@2.2.1:
    dependencies:
      copy-anything: 3.0.5

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  svg-tags@1.0.0: {}

  synckit@0.9.2:
    dependencies:
      '@pkgr/core': 0.1.1
      tslib: 2.8.1

  tabbable@6.2.0: {}

  tar@6.2.1:
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0
    optional: true

  terser@5.37.0:
    dependencies:
      '@jridgewell/source-map': 0.3.6
      acorn: 8.14.0
      commander: 2.20.3
      source-map-support: 0.5.21
    optional: true

  tinyexec@0.3.1: {}

  tinyglobby@0.2.10:
    dependencies:
      fdir: 6.4.2(picomatch@4.0.2)
      picomatch: 4.0.2

  tippy.js@6.3.7:
    dependencies:
      '@popperjs/core': 2.11.8

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  totalist@3.0.1: {}

  trim-lines@3.0.1: {}

  tslib@2.4.0: {}

  tslib@2.8.1: {}

  tsx@4.19.2:
    dependencies:
      esbuild: 0.23.1
      get-tsconfig: 4.8.1
    optionalDependencies:
      fsevents: 2.3.3

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@0.20.2: {}

  typescript@5.7.2:
    optional: true

  uc.micro@1.0.6: {}

  uc.micro@2.1.0: {}

  ufo@1.5.4: {}

  unconfig@0.5.5:
    dependencies:
      '@antfu/utils': 0.7.10
      defu: 6.1.4
      importx: 0.4.4
    transitivePeerDependencies:
      - supports-color

  uncrypto@0.1.3:
    optional: true

  unctx@2.3.1:
    dependencies:
      acorn: 8.14.0
      estree-walker: 3.0.3
      magic-string: 0.30.14
      unplugin: 1.16.0
    optional: true

  undici-types@6.20.0:
    optional: true

  unicorn-magic@0.1.0:
    optional: true

  unicorn-magic@0.3.0: {}

  unimport@3.14.3(rollup@4.28.0):
    dependencies:
      '@rollup/pluginutils': 5.1.3(rollup@4.28.0)
      acorn: 8.14.0
      escape-string-regexp: 5.0.0
      estree-walker: 3.0.3
      local-pkg: 0.5.1
      magic-string: 0.30.14
      mlly: 1.7.3
      pathe: 1.1.2
      picomatch: 4.0.2
      pkg-types: 1.2.1
      scule: 1.3.0
      strip-literal: 2.1.1
      tinyglobby: 0.2.10
      unplugin: 1.16.0
    transitivePeerDependencies:
      - rollup

  unist-util-is@6.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-position@5.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-stringify-position@4.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-visit-parents@6.0.1:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0

  unist-util-visit@5.0.0:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0
      unist-util-visit-parents: 6.0.1

  universalify@2.0.1: {}

  unocss@0.63.6(postcss@8.4.49)(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0)):
    dependencies:
      '@unocss/astro': 0.63.6(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))
      '@unocss/cli': 0.63.6(rollup@4.28.0)
      '@unocss/core': 0.63.6
      '@unocss/postcss': 0.63.6(postcss@8.4.49)
      '@unocss/preset-attributify': 0.63.6
      '@unocss/preset-icons': 0.63.6
      '@unocss/preset-mini': 0.63.6
      '@unocss/preset-tagify': 0.63.6
      '@unocss/preset-typography': 0.63.6
      '@unocss/preset-uno': 0.63.6
      '@unocss/preset-web-fonts': 0.63.6
      '@unocss/preset-wind': 0.63.6
      '@unocss/transformer-attributify-jsx': 0.63.6
      '@unocss/transformer-compile-class': 0.63.6
      '@unocss/transformer-directives': 0.63.6
      '@unocss/transformer-variant-group': 0.63.6
      '@unocss/vite': 0.63.6(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))
    optionalDependencies:
      vite: 5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0)
    transitivePeerDependencies:
      - postcss
      - rollup
      - supports-color
      - typescript

  unocss@0.63.6(postcss@8.4.49)(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)):
    dependencies:
      '@unocss/astro': 0.63.6(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))
      '@unocss/cli': 0.63.6(rollup@4.28.0)
      '@unocss/core': 0.63.6
      '@unocss/postcss': 0.63.6(postcss@8.4.49)
      '@unocss/preset-attributify': 0.63.6
      '@unocss/preset-icons': 0.63.6
      '@unocss/preset-mini': 0.63.6
      '@unocss/preset-tagify': 0.63.6
      '@unocss/preset-typography': 0.63.6
      '@unocss/preset-uno': 0.63.6
      '@unocss/preset-web-fonts': 0.63.6
      '@unocss/preset-wind': 0.63.6
      '@unocss/transformer-attributify-jsx': 0.63.6
      '@unocss/transformer-compile-class': 0.63.6
      '@unocss/transformer-directives': 0.63.6
      '@unocss/transformer-variant-group': 0.63.6
      '@unocss/vite': 0.63.6(rollup@4.28.0)(typescript@5.7.2)(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))
    optionalDependencies:
      vite: 5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)
    transitivePeerDependencies:
      - postcss
      - rollup
      - supports-color
      - typescript

  unplugin-auto-import@0.18.6(@nuxt/kit@3.14.1592(rollup@4.28.0))(@vueuse/core@11.3.0(vue@3.5.13(typescript@5.7.2)))(rollup@4.28.0):
    dependencies:
      '@antfu/utils': 0.7.10
      '@rollup/pluginutils': 5.1.3(rollup@4.28.0)
      fast-glob: 3.3.2
      local-pkg: 0.5.1
      magic-string: 0.30.14
      minimatch: 9.0.5
      unimport: 3.14.3(rollup@4.28.0)
      unplugin: 1.16.0
    optionalDependencies:
      '@nuxt/kit': 3.14.1592(rollup@4.28.0)
      '@vueuse/core': 11.3.0(vue@3.5.13(typescript@5.7.2))
    transitivePeerDependencies:
      - rollup

  unplugin-icons@0.20.2(@vue/compiler-sfc@3.5.13):
    dependencies:
      '@antfu/install-pkg': 0.5.0
      '@antfu/utils': 0.7.10
      '@iconify/utils': 2.1.33
      debug: 4.3.7
      kolorist: 1.8.0
      local-pkg: 0.5.1
      unplugin: 1.16.0
    optionalDependencies:
      '@vue/compiler-sfc': 3.5.13
    transitivePeerDependencies:
      - supports-color

  unplugin-vue-components@0.27.5(@babel/parser@7.26.3)(@nuxt/kit@3.14.1592(rollup@4.28.0))(rollup@4.28.0)(vue@3.5.13(typescript@5.7.2)):
    dependencies:
      '@antfu/utils': 0.7.10
      '@rollup/pluginutils': 5.1.3(rollup@4.28.0)
      chokidar: 3.6.0
      debug: 4.3.7
      fast-glob: 3.3.2
      local-pkg: 0.5.1
      magic-string: 0.30.14
      minimatch: 9.0.5
      mlly: 1.7.3
      unplugin: 1.16.0
      vue: 3.5.13(typescript@5.7.2)
    optionalDependencies:
      '@babel/parser': 7.26.3
      '@nuxt/kit': 3.14.1592(rollup@4.28.0)
    transitivePeerDependencies:
      - rollup
      - supports-color

  unplugin@1.16.0:
    dependencies:
      acorn: 8.14.0
      webpack-virtual-modules: 0.6.2

  untyped@1.5.1:
    dependencies:
      '@babel/core': 7.26.0
      '@babel/standalone': 7.26.4
      '@babel/types': 7.26.3
      defu: 6.1.4
      jiti: 2.4.1
      mri: 1.2.0
      scule: 1.3.0
    transitivePeerDependencies:
      - supports-color
    optional: true

  update-browserslist-db@1.1.1(browserslist@4.24.2):
    dependencies:
      browserslist: 4.24.2
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  util-deprecate@1.0.2: {}

  uuid@11.0.3: {}

  vfile-message@4.0.2:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-stringify-position: 4.0.0

  vfile@6.0.3:
    dependencies:
      '@types/unist': 3.0.3
      vfile-message: 4.0.2

  vite-hot-client@0.2.4(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0)):
    dependencies:
      vite: 5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0)

  vite-hot-client@0.2.4(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)):
    dependencies:
      vite: 5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)

  vite-plugin-inspect@0.8.8(@nuxt/kit@3.14.1592(rollup@4.28.0))(rollup@4.28.0)(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0)):
    dependencies:
      '@antfu/utils': 0.7.10
      '@rollup/pluginutils': 5.1.3(rollup@4.28.0)
      debug: 4.3.7
      error-stack-parser-es: 0.1.5
      fs-extra: 11.2.0
      open: 10.1.0
      perfect-debounce: 1.0.0
      picocolors: 1.1.1
      sirv: 3.0.0
      vite: 5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0)
    optionalDependencies:
      '@nuxt/kit': 3.14.1592(rollup@4.28.0)
    transitivePeerDependencies:
      - rollup
      - supports-color

  vite-plugin-inspect@0.8.8(@nuxt/kit@3.14.1592(rollup@4.28.0))(rollup@4.28.0)(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)):
    dependencies:
      '@antfu/utils': 0.7.10
      '@rollup/pluginutils': 5.1.3(rollup@4.28.0)
      debug: 4.3.7
      error-stack-parser-es: 0.1.5
      fs-extra: 11.2.0
      open: 10.1.0
      perfect-debounce: 1.0.0
      picocolors: 1.1.1
      sirv: 3.0.0
      vite: 5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)
    optionalDependencies:
      '@nuxt/kit': 3.14.1592(rollup@4.28.0)
    transitivePeerDependencies:
      - rollup
      - supports-color

  vite-plugin-vue-devtools@7.6.7(@nuxt/kit@3.14.1592(rollup@4.28.0))(rollup@4.28.0)(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2)):
    dependencies:
      '@vue/devtools-core': 7.6.7(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))
      '@vue/devtools-kit': 7.6.7
      '@vue/devtools-shared': 7.6.7
      execa: 9.5.1
      sirv: 3.0.0
      vite: 5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0)
      vite-plugin-inspect: 0.8.8(@nuxt/kit@3.14.1592(rollup@4.28.0))(rollup@4.28.0)(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))
      vite-plugin-vue-inspector: 5.3.1(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0))
    transitivePeerDependencies:
      - '@nuxt/kit'
      - rollup
      - supports-color
      - vue

  vite-plugin-vue-devtools@7.6.7(@nuxt/kit@3.14.1592(rollup@4.28.0))(rollup@4.28.0)(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2)):
    dependencies:
      '@vue/devtools-core': 7.6.7(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))
      '@vue/devtools-kit': 7.6.7
      '@vue/devtools-shared': 7.6.7
      execa: 9.5.1
      sirv: 3.0.0
      vite: 5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)
      vite-plugin-inspect: 0.8.8(@nuxt/kit@3.14.1592(rollup@4.28.0))(rollup@4.28.0)(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))
      vite-plugin-vue-inspector: 5.3.1(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))
    transitivePeerDependencies:
      - '@nuxt/kit'
      - rollup
      - supports-color
      - vue

  vite-plugin-vue-inspector@5.3.1(vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0)):
    dependencies:
      '@babel/core': 7.26.0
      '@babel/plugin-proposal-decorators': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-syntax-import-attributes': 7.26.0(@babel/core@7.26.0)
      '@babel/plugin-syntax-import-meta': 7.10.4(@babel/core@7.26.0)
      '@babel/plugin-transform-typescript': 7.26.3(@babel/core@7.26.0)
      '@vue/babel-plugin-jsx': 1.2.5(@babel/core@7.26.0)
      '@vue/compiler-dom': 3.5.13
      kolorist: 1.8.0
      magic-string: 0.30.14
      vite: 5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0)
    transitivePeerDependencies:
      - supports-color

  vite-plugin-vue-inspector@5.3.1(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)):
    dependencies:
      '@babel/core': 7.26.0
      '@babel/plugin-proposal-decorators': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-syntax-import-attributes': 7.26.0(@babel/core@7.26.0)
      '@babel/plugin-syntax-import-meta': 7.10.4(@babel/core@7.26.0)
      '@babel/plugin-transform-typescript': 7.26.3(@babel/core@7.26.0)
      '@vue/babel-plugin-jsx': 1.2.5(@babel/core@7.26.0)
      '@vue/compiler-dom': 3.5.13
      kolorist: 1.8.0
      magic-string: 0.30.14
      vite: 5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)
    transitivePeerDependencies:
      - supports-color

  vite@5.4.11(@types/node@22.10.1)(less@4.4.0)(sass@1.82.0)(terser@5.37.0):
    dependencies:
      esbuild: 0.21.5
      postcss: 8.4.49
      rollup: 4.28.0
    optionalDependencies:
      '@types/node': 22.10.1
      fsevents: 2.3.3
      less: 4.4.0
      sass: 1.82.0
      terser: 5.37.0

  vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0):
    dependencies:
      esbuild: 0.21.5
      postcss: 8.4.49
      rollup: 4.28.0
    optionalDependencies:
      '@types/node': 22.10.1
      fsevents: 2.3.3
      sass: 1.82.0
      terser: 5.37.0

  vitepress@1.0.0-rc.44(@algolia/client-search@5.15.0)(@types/node@22.10.1)(postcss@8.4.49)(sass@1.82.0)(search-insights@2.17.3)(terser@5.37.0)(typescript@5.7.2):
    dependencies:
      '@docsearch/css': 3.8.0
      '@docsearch/js': 3.8.0(@algolia/client-search@5.15.0)(search-insights@2.17.3)
      '@shikijs/core': 1.24.0
      '@shikijs/transformers': 1.24.0
      '@types/markdown-it': 13.0.9
      '@vitejs/plugin-vue': 5.2.1(vite@5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0))(vue@3.5.13(typescript@5.7.2))
      '@vue/devtools-api': 7.6.7
      '@vueuse/core': 10.11.1(vue@3.5.13(typescript@5.7.2))
      '@vueuse/integrations': 10.11.1(focus-trap@7.6.2)(vue@3.5.13(typescript@5.7.2))
      focus-trap: 7.6.2
      mark.js: 8.11.1
      minisearch: 6.3.0
      shiki: 1.24.0
      vite: 5.4.11(@types/node@22.10.1)(sass@1.82.0)(terser@5.37.0)
      vue: 3.5.13(typescript@5.7.2)
    optionalDependencies:
      postcss: 8.4.49
    transitivePeerDependencies:
      - '@algolia/client-search'
      - '@types/node'
      - '@types/react'
      - '@vue/composition-api'
      - async-validator
      - axios
      - change-case
      - drauu
      - fuse.js
      - idb-keyval
      - jwt-decode
      - less
      - lightningcss
      - nprogress
      - qrcode
      - react
      - react-dom
      - sass
      - sass-embedded
      - search-insights
      - sortablejs
      - stylus
      - sugarss
      - terser
      - typescript
      - universal-cookie

  vue-demi@0.14.10(vue@3.5.13(typescript@5.7.2)):
    dependencies:
      vue: 3.5.13(typescript@5.7.2)

  vue-eslint-parser@9.4.3(eslint@9.16.0(jiti@2.4.1)):
    dependencies:
      debug: 4.3.7
      eslint: 9.16.0(jiti@2.4.1)
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.6.0
      lodash: 4.17.21
      semver: 7.6.3
    transitivePeerDependencies:
      - supports-color

  vue-flow-layout@0.0.5(typescript@5.7.2):
    dependencies:
      vue: 3.5.13(typescript@5.7.2)
    transitivePeerDependencies:
      - typescript

  vue-i18n@10.0.5(vue@3.5.13(typescript@5.7.2)):
    dependencies:
      '@intlify/core-base': 10.0.5
      '@intlify/shared': 10.0.5
      '@vue/devtools-api': 6.6.4
      vue: 3.5.13(typescript@5.7.2)

  vue-router@4.5.0(vue@3.5.13(typescript@5.7.2)):
    dependencies:
      '@vue/devtools-api': 6.6.4
      vue: 3.5.13(typescript@5.7.2)

  vue@3.5.13(typescript@5.7.2):
    dependencies:
      '@vue/compiler-dom': 3.5.13
      '@vue/compiler-sfc': 3.5.13
      '@vue/runtime-dom': 3.5.13
      '@vue/server-renderer': 3.5.13(vue@3.5.13(typescript@5.7.2))
      '@vue/shared': 3.5.13
    optionalDependencies:
      typescript: 5.7.2

  w3c-keyname@2.2.8: {}

  webpack-virtual-modules@0.6.2: {}

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  word-wrap@1.2.5: {}

  xml-name-validator@4.0.0: {}

  y-prosemirror@1.3.6(prosemirror-model@1.24.0)(prosemirror-state@1.4.3)(prosemirror-view@1.37.0)(y-protocols@1.0.6(yjs@13.6.27))(yjs@13.6.27):
    dependencies:
      lib0: 0.2.109
      prosemirror-model: 1.24.0
      prosemirror-state: 1.4.3
      prosemirror-view: 1.37.0
      y-protocols: 1.0.6(yjs@13.6.27)
      yjs: 13.6.27

  y-protocols@1.0.6(yjs@13.6.27):
    dependencies:
      lib0: 0.2.109
      yjs: 13.6.27

  y-websocket@3.0.0(yjs@13.6.27):
    dependencies:
      lib0: 0.2.109
      y-protocols: 1.0.6(yjs@13.6.27)
      yjs: 13.6.27

  yallist@3.1.1: {}

  yallist@4.0.0:
    optional: true

  yjs@13.6.27:
    dependencies:
      lib0: 0.2.109

  yocto-queue@0.1.0: {}

  yoctocolors@2.1.1: {}

  zwitch@2.0.4: {}
