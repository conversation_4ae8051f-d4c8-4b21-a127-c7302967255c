---
inclusion: always
---

# 富文本编辑器项目开发指南

## 项目概述

这是一个基于 Vue 3 + TipTap + Yjs 的协作富文本编辑器项目，支持实时协作编辑功能。

### 技术栈
- **前端**: Vue 3 + Vite + TipTap + UnoCSS + Arco Design
- **后端**: Koa.js + Socket.io + Y-WebSocket
- **协作**: Yjs + Y-WebSocket + Y-LevelDB
- **构建工具**: Vite + ESLint + Prettier

## 架构模式

### 分层架构
```
app/src/packages/
├── core/           # 核心编辑器逻辑（框架无关）
├── vue3/           # Vue 3 特定实现
│   ├── components/ # UI 组件
│   ├── styles/     # 样式文件
│   └── utils/      # 工具函数
```

### 编辑器扩展系统
- 所有扩展位于 `app/src/packages/core/extensions/`
- 每个扩展应该是独立的模块
- 扩展通过 `app/src/packages/core/extensions/index.js` 统一导出

## 代码规范

### 命名约定
- **文件名**: kebab-case (例: `bubble-menu.js`)
- **组件名**: PascalCase (例: `BubbleMenu`)
- **变量/函数**: camelCase (例: `editorInstance`)
- **常量**: UPPER_SNAKE_CASE (例: `DEFAULT_OPTIONS`)

### 文件组织
- 组件文件使用 `.vue` 扩展名
- 工具函数使用 `.js` 或 `.ts` 扩展名
- 样式文件使用 `.scss` 扩展名
- 类型定义使用 `.d.ts` 扩展名

### Vue 3 特定规范
- 优先使用 Composition API
- 使用 `<script setup>` 语法
- 响应式数据使用 `ref()` 或 `reactive()`
- 避免在模板中使用复杂表达式

### TipTap 扩展开发
- 扩展应继承自 `@tiptap/core` 的基础类
- 使用 `addOptions()` 定义可配置选项
- 使用 `addCommands()` 添加编辑器命令
- 使用 `addKeyboardShortcuts()` 添加快捷键

## 性能优化

### 编辑器性能
- 使用 `useDebouncedRef` 优化响应式更新
- 避免在编辑器事件中执行重计算
- 合理使用 `markRaw()` 防止不必要的响应式转换

### 协作性能
- Yjs 文档更新应该批量处理
- 避免频繁的网络同步
- 使用适当的 debounce 延迟

## 安全考虑

### 内容安全
- 所有用户输入必须经过 XSS 过滤
- 图片上传需要验证文件类型和大小
- 链接需要验证协议安全性

### 协作安全
- WebSocket 连接需要身份验证
- 文档访问权限控制
- 防止恶意用户破坏文档结构

## 开发工作流

### 新功能开发
1. 在 `core/extensions/` 创建扩展
2. 在 `vue3/components/` 创建 UI 组件
3. 在 `vue3/styles/` 添加样式
4. 更新 `extensions/index.js` 导出
5. 在相应的 kit 中注册扩展

### 样式开发
- 使用 SCSS 预处理器
- 遵循 BEM 命名规范
- 使用 CSS 变量进行主题定制
- 响应式设计优先

### 测试策略
- 单元测试覆盖核心逻辑
- 集成测试验证编辑器功能
- E2E 测试验证协作功能
- 性能测试确保编辑器流畅性

## 常见模式

### 编辑器初始化
```javascript
import { Editor } from '@/packages/vue3'
import { BasicKit } from '@/packages/vue3/kit'

const editor = new Editor({
  extensions: [BasicKit],
  content: '<p>Hello World!</p>'
})
```

### 扩展注册
```javascript
// 在 extensions/index.js 中
export { Bold } from './bold.js'
export { Italic } from './italic.js'

// 在 kit 中使用
import { Bold, Italic } from '../extensions'
export const BasicKit = [Bold, Italic]
```

### 响应式状态管理
```javascript
// 使用 Vue 3 响应式 API
const editorState = ref(null)
const isEditable = computed(() => editor.value?.isEditable)
```

## 调试指南

### 编辑器调试
- 使用 `editor.view.state` 查看 ProseMirror 状态
- 使用 `editor.extensionStorage` 查看扩展存储
- 启用 TipTap 开发工具进行调试

### 协作调试
- 检查 WebSocket 连接状态
- 监控 Yjs 文档更新事件
- 使用浏览器开发工具查看网络请求

## 部署注意事项

### 构建配置
- 生产环境禁用 Vue DevTools
- 优化 bundle 大小，按需加载扩展
- 配置正确的 publicPath

### 服务器配置
- WebSocket 代理配置
- 静态资源缓存策略
- CORS 跨域配置