<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        #results { margin-top: 20px; padding: 10px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>px-doc 文档API测试</h1>
    
    <div class="test-section">
        <h3>测试创建文档</h3>
        <input type="text" id="docName" placeholder="输入文档名称" value="测试文档">
        <button onclick="testCreateDocument()">创建文档</button>
    </div>
    
    <div class="test-section">
        <h3>测试获取文档列表</h3>
        <button onclick="testGetDocuments()">获取文档列表</button>
    </div>
    
    <div id="results">
        <h4>测试结果:</h4>
        <div id="output"></div>
    </div>

    <script>
        const API_BASE = 'http://*************:3002/api/v1';
        
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            output.appendChild(div);
            console.log(message);
        }
        
        async function testCreateDocument() {
            const name = document.getElementById('docName').value;
            if (!name) {
                log('请输入文档名称', 'error');
                return;
            }
            
            try {
                log(`正在创建文档: ${name}`, 'info');
                
                const response = await fetch(`${API_BASE}/documents`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ name })
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    log(`✅ 创建文档成功: ${JSON.stringify(data.data)}`, 'success');
                    log(`📄 文档ID: ${data.data.id}`, 'info');
                    log(`🔗 访问链接: /px-editor/${data.data.id}`, 'info');
                } else {
                    log(`❌ 创建文档失败: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }
        
        async function testGetDocuments() {
            try {
                log('正在获取文档列表...', 'info');
                
                const response = await fetch(`${API_BASE}/documents`);
                const data = await response.json();
                
                if (data.code === 200) {
                    log(`✅ 获取文档列表成功 (${data.data.length}个文档)`, 'success');
                    data.data.forEach(doc => {
                        log(`📄 ${doc.name} (ID: ${doc.id})`, 'info');
                    });
                } else {
                    log(`❌ 获取文档列表失败: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动测试连接
        window.onload = function() {
            log('🚀 页面加载完成，开始测试API连接...', 'info');
            testGetDocuments();
        };
    </script>
</body>
</html>